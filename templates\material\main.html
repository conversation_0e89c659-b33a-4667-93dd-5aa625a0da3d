{% load humanize %}
<div class="content-header" id="content-header">
    <span class="material-icons">arrow_back</span>
    <h2 class="page-title">
        <span x-text="pageTitle"></span>
    </h2>
    <div class="actions">
    </div>
</div>
<div class="content-area" id="content-area">
    <div class="dashboard-container">
        <!-- Quick Stats Cards -->
        <div class="stats-grid">
            <div class="stat-card-column">
                <div class="stat-card-header">
                    <div class="stat-icon">
                        <span class="material-icons">groups_2</span>
                    </div>
                    <div class="stat-header-content">
                        <div class="stat-label-main">Élèves inscrits</div>
                        <div class="stat-change positive">+12 ce mois</div>
                    </div>
                </div>
                <div class="stat-grid-content">
                    <div class="stat-grid-item">
                        <div class="stat-number-small">{{ data.boys|intcomma }}</div>
                        <div class="stat-label-small">Garçons</div>
                    </div>
                    <div class="stat-grid-item">
                        <div class="stat-number-small">{{data.girls|intcomma }}</div>
                        <div class="stat-label-small">Filles</div>
                    </div>
                    <div class="stat-grid-item stat-grid-total">
                        <div class="stat-number-small">{{ data.students|intcomma }}</div>
                        <div class="stat-label-small">Total</div>
                    </div>
                </div>
            </div>
            <div class="stat-card-column">
                <div class="stat-card-header">
                    <div class="stat-icon">
                        <span class="material-icons">money</span>
                    </div>
                    <div class="stat-header-content">
                        <div class="stat-label-main">Paiements encaissé aujourd'hui</div>
                    </div>
                </div>
                <div class="stat-grid-content">
                    <div class="stat-grid-item">
                        <div class="stat-number-small">{{ data.inscription_today|intcomma }}</div>
                        <div class="stat-label-small">Inscription</div>
                    </div>
                    <div class="stat-grid-item">
                        <div class="stat-number-small">{{ data.total_scolarite_annexe_today|intcomma }}</div>
                        <div class="stat-label-small">Scolarité + Annexe</div>
                    </div>
                    <div class="stat-grid-item stat-grid-total">
                        <div class="stat-number-small">{{ data.total_paid_today|intcomma }}</div>
                        <div class="stat-label-small">Total</div>
                    </div>
                </div>
            </div>
            <div class="stat-card-column">
                <div class="stat-card-header">
                    <div class="stat-icon">
                        <span class="material-icons">money</span>
                    </div>
                    <div class="stat-header-content">
                        <div class="stat-label-main">Cumul paiements 2024-2025</div>
                    </div>
                </div>
                <div class="stat-grid-content">
                    <div class="stat-grid-item">
                        <div class="stat-number-small">{{ data.inscription|intcomma }}</div>
                        <div class="stat-label-small">Inscription</div>
                    </div>
                    <div class="stat-grid-item">
                        <div class="stat-number-small">{{ data.total_scolarite_annexe|intcomma }}</div>
                        <div class="stat-label-small">Scolarité + Annexe</div>
                    </div>
                    <div class="stat-grid-item stat-grid-total">
                        <div class="stat-number-small">{{ data.total_paid|intcomma }}</div>
                        <div class="stat-label-small">Total</div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Charts and Analytics Section -->
        <div class="dashboard-grid">
            <!-- Monthly Payments Chart -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Évolution des paiements</h3>
                    <div class="card-actions">
                        <div class="mdc-menu-surface--anchor">
                            <button class="mdc-icon-button material-icons" id="paymentsChartMenuButton">more_vert</button>
                            <div class="mdc-menu mdc-menu-surface" id="paymentsChartMenu">
                                <ul class="mdc-list" role="menu" aria-hidden="true" aria-orientation="vertical">
                                    <li class="mdc-list-item" role="menuitem" onclick="refreshChart('payments')">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="mdc-list-item__graphic material-icons">refresh</span>
                                        <span class="mdc-list-item__text">Actualiser</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-content">
                    <canvas id="enrollmentChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Payment Status Overview -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>État des paiements</h3>
                    <div class="card-actions">
                        <div class="mdc-menu-surface--anchor">
                            <button class="mdc-icon-button material-icons" id="statusChartMenuButton">more_vert</button>
                            <div class="mdc-menu mdc-menu-surface" id="statusChartMenu">
                                <ul class="mdc-list" role="menu" aria-hidden="true" aria-orientation="vertical">
                                    <li class="mdc-list-item" role="menuitem" onclick="refreshChart('status')">
                                        <span class="mdc-list-item__ripple"></span>
                                        <span class="mdc-list-item__graphic material-icons">refresh</span>
                                        <span class="mdc-list-item__text">Actualiser</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-content">
                    <canvas id="paymentChart" width="400" height="200"></canvas>
                </div>
            </div>
        
        <!-- Quick Actions -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3>Actions rapides</h3>
            </div>
            <div class="card-content">
                <div class="quick-actions-grid">
                    <div class="quick-action-item">
                        <div class="quick-action-icon">
                            <span class="material-icons">person_add</span>
                        </div>
                        <div class="quick-action-label">Ajouter un élève</div>
                    </div>
                    <div class="quick-action-item">
                        <div class="quick-action-icon">
                            <span class="material-icons">payment</span>
                        </div>
                        <div class="quick-action-label">Enregistrer un paiement</div>
                    </div>
                    <div class="quick-action-item">
                        <div class="quick-action-icon">
                            <span class="material-icons">assignment</span>
                        </div>
                        <div class="quick-action-label">Générer un bulletin</div>
                    </div>
                    <div class="quick-action-item">
                        <div class="quick-action-icon">
                            <span class="material-icons">list_alt</span>
                        </div>
                        <div class="quick-action-label">Liste de classe</div>
                    </div>
                    <!-- <div class="quick-action-item">
                        <div class="quick-action-icon">
                            <span class="material-icons">event_busy</span>
                        </div>
                        <div class="quick-action-label">Marquer une absence</div>
                    </div>
                    <div class="quick-action-item">
                        <div class="quick-action-icon">
                            <span class="material-icons">assessment</span>
                        </div>
                        <div class="quick-action-label">Rapport mensuel</div>
                    </div> -->
                </div>
            </div>
        </div>
        </div>
        </div>
</div>
    <!-- Chart Data -->
    <script>
        // Pass Django data to JavaScript
        window.chartData = {
            boys: {{ data.boys|default:0 }},
            girls: {{ data.girls|default:0 }},
            students: {{ data.students|default:0 }},
            inscription: {{ data.inscription|default:0 }},
            total_scolarite_annexe: {{ data.total_scolarite_annexe|default:0 }},
            monthly_payments: [{% for payment in data.monthly_payments %}{{ payment }}{% if not forloop.last %},{% endif %}{% endfor %}]
        };

        // Make refreshChart function globally accessible
        window.refreshChart = function(chartType) {
            // Show loading state
            const loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }

            // Fetch fresh data from API
            fetch('/api/chart-data/')
                .then(response => response.json())
                .then(data => {
                    // Update global chart data
                    window.chartData = data;

                    // Reinitialize charts with new data
                    if (window.initializeDashboardCharts) {
                        window.initializeDashboardCharts(data);
                    }

                    // Reinitialize menus
                    if (window.initializeMaterialMenus) {
                        window.initializeMaterialMenus();
                    }

                    // Hide loading state
                    if (loadingOverlay) {
                        loadingOverlay.style.display = 'none';
                    }

                    console.log(`${chartType} chart refreshed successfully`);
                })
                .catch(error => {
                    console.error('Error refreshing chart data:', error);

                    // Hide loading state
                    if (loadingOverlay) {
                        loadingOverlay.style.display = 'none';
                    }
                });
            // Initialize status chart menu
            var statusMenuButton = document.getElementById('statusChartMenuButton');
            var statusMenu = document.getElementById('statusChartMenu');

            if (statusMenuButton && statusMenu && typeof mdc !== 'undefined') {
                window.statusMenuInstance = new mdc.menu.MDCMenu(statusMenu);
                statusMenuButton.addEventListener('click', () => {
                    window.statusMenuInstance.open = !window.statusMenuInstance.open;
                });
            }
        };
    </script>

</div>
