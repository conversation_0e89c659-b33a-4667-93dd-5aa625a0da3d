/**
 * MDC Auto-Initialization for Template Tags
 * Provides automatic initialization of Material Design Components
 */

(function() {
    'use strict';

    // Global initialization function
    window.initMDCField = function(containerId, componentType) {
        const element = document.getElementById(containerId);
        if (!element || typeof mdc === 'undefined') {
            console.warn(`MDC initialization failed: element ${containerId} not found or MDC not loaded`);
            return;
        }
        
        try {
            switch(componentType) {
                case 'textField':
                    if (mdc.textField && !element.mdcTextField) {
                        element.mdcTextField = new mdc.textField.MDCTextField(element);
                    }
                    break;
                    
                case 'select':
                    if (mdc.select && !element.mdcSelect) {
                        const selectComponent = new mdc.select.MDCSelect(element);
                        element.mdcSelect = selectComponent;
                        
                        // Handle HTMX triggers on select change
                        selectComponent.listen('MDCSelect:change', () => {
                            const anchor = element.querySelector('.mdc-select__anchor');
                            if (anchor && (anchor.hasAttribute('hx-get') || anchor.hasAttribute('hx-post'))) {
                                if (typeof htmx !== 'undefined') {
                                    htmx.trigger(anchor, 'change');
                                }
                            }
                        });
                    }
                    break;
                    
                case 'checkbox':
                    const checkboxElement = element.querySelector('.mdc-checkbox');
                    if (mdc.checkbox && checkboxElement && !checkboxElement.mdcCheckbox) {
                        checkboxElement.mdcCheckbox = new mdc.checkbox.MDCCheckbox(checkboxElement);
                    }
                    if (mdc.formField && !element.mdcFormField) {
                        element.mdcFormField = new mdc.formField.MDCFormField(element);
                        if (checkboxElement && checkboxElement.mdcCheckbox) {
                            element.mdcFormField.input = checkboxElement.mdcCheckbox;
                        }
                    }
                    break;
                    
                default:
                    console.warn(`Unknown MDC component type: ${componentType}`);
            }
        } catch (error) {
            console.error(`Failed to initialize MDC ${componentType}:`, error);
        }
    };

    // Batch initialization function for multiple components
    window.initAllMDCFields = function(container = document) {
        // Initialize text fields
        container.querySelectorAll('.mdc-text-field').forEach(element => {
            if (!element.mdcTextField && element.id) {
                initMDCField(element.id, 'textField');
            }
        });

        // Initialize select fields
        container.querySelectorAll('.mdc-select').forEach(element => {
            if (!element.mdcSelect && element.id) {
                initMDCField(element.id, 'select');
            }
        });

        // Initialize checkboxes
        container.querySelectorAll('.mdc-form-field').forEach(element => {
            const checkbox = element.querySelector('.mdc-checkbox');
            if (checkbox && !element.mdcFormField && element.id) {
                initMDCField(element.id, 'checkbox');
            }
        });
    };

    // Auto-initialize on DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => initAllMDCFields());
    } else {
        initAllMDCFields();
    }

    // Auto-initialize after HTMX requests
    if (typeof htmx !== 'undefined') {
        document.addEventListener('htmx:afterRequest', function(event) {
            // Small delay to ensure DOM is updated
            setTimeout(() => {
                initAllMDCFields(event.detail.target || document);
            }, 50);
        });
    }

})();
