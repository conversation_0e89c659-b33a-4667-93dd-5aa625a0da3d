{% load humanize %}
{% load static %}

<div class="content-header" id="content-header" hx-swap-oob="true">
    <span class="material-icons">arrow_back</span>
    <h2 class="page-title">
        <span>Gestion des élèves</span>
    </h2>
    <div class="actions">
        <button class="mdc-button mdc-button--raised add-btn" id="add-student-btn">
            <span class="mdc-button__ripple"></span>
            <span class="material-icons" style="pointer-events: none;">add</span>
            <span class="mdc-button__label">Ajouter un élève</span>
        </button>
        <span class="material-icons search-icon-mobile" title="Rechercher" id="mobile-search-btn"
              onclick="console.log('Search icon clicked!'); if(typeof openMobileSearch === 'function') openMobileSearch(); else console.error('openMobileSearch not found');">search</span>
        <span class="material-icons" title="Filtrer" id="filter-btn">filter_list</span>
    </div>
</div>

<div class="content-area" id="content-area">
    <!-- Quick Filter Chips -->
    <div class="quick-filter-chips">
        <button class="quick-filter-chip {% if not 'filter_by' in request.GET or not request.GET.filter_by or request.GET.filter_by == 'all' %} active {% endif %}"
                data-filter="all"
                onclick="createRipple(event, this); changeFilter('all')">
            <span class="material-icons">group</span>
            <span>Tous</span>
        </button>
        <button class="quick-filter-chip {% if request.GET.filter_by == 'active' %} active {% endif %}"
                data-filter="active"
                onclick="createRipple(event, this); changeFilter('active')">
            <span class="material-icons">check_circle</span>
            <span>Actifs</span>
        </button>
        <button class="quick-filter-chip {% if request.GET.filter_by == 'inactive' %} active {% endif %}"
                data-filter="inactive"
                onclick="createRipple(event, this); changeFilter('inactive')">
            <span class="material-icons">cancel</span>
            <span>Inactifs</span>
        </button>
    </div>

    <!-- Students List (Mobile) -->
    <div class="students-list" id="students-list">
        {% include 'material/partials/student_items.html' with enrollments=enrollments %}
        {% if not enrollments %}
            <!-- Empty State -->
            <div class="empty-state">
                <span class="material-icons">search_off</span>
                <div class="empty-text">Aucun élève trouvé</div>
            </div>
        {% endif %}
    </div>

    <!-- Loading Indicator -->
    <div class="infinite-scroll-loading" id="infinite-scroll-loading" style="display: none;">
        <div class="loading-spinner">
            <div class="mdc-circular-progress mdc-circular-progress--indeterminate">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="24" cy="24" r="18" stroke-width="4"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="113.097" stroke-width="4"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="3.2"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="loading-text">Chargement...</div>
    </div>

    <!-- End of List Indicator -->
    <div class="end-of-list" id="end-of-list" style="display: none;">
        <div class="end-of-list-content">
            <span class="material-icons">check_circle</span>
            <div class="end-of-list-text">Fin de la liste</div>
        </div>
    </div>


</div>

<!-- QR Code Floating Action Button -->
<button class="mdc-fab qr-fab" id="qr-fab" title="Scanner QR Code">
    <div class="mdc-fab__ripple"></div>
    <span class="material-icons mdc-fab__icon">qr_code</span>
</button>

<!-- Add Student Floating Action Button -->
<button class="mdc-fab mdc-fab--extended" id="add-student-fab" title="Ajouter un élève"
        hx-get="{% url 'school:student_add_wizard' %}"
        hx-target="#modal-content"
        hx-trigger="click"
        hx-swap="innerHTML"
        hx-on::after-request="if(event.detail.xhr.status === 200) { showModal(); }"
        hx-on::before-request="console.log('Loading student wizard...')">
    <div class="mdc-fab__ripple"></div>
    <span class="material-icons mdc-fab__icon">add</span>
    <span class="mdc-fab__label">Ajouter un élève</span>
</button>

<!-- <script src="{% static 'material/js/table-row-selection.js' %}"></script>
<script src="{% static 'material/js/floating-actions.js' %}"></script> -->

<style>
/* Student Card Styles */
.student-photo {
    width: 60px !important;
    height: 60px !important;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    flex-shrink: 0;
}

.student-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.student-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary, #333);
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.student-id {
    color: var(--text-primary, #333);
    font-size: 14px;
    font-weight: 500;
    margin-left: 12px;
}

/* Student Info Row Styles */
.student-info-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 4px;
    gap: 12px;
    padding: 1px 0;
}

.info-label {
    color: var(--text-secondary, #666);
    font-size: 13px;
    font-weight: 500;
    flex-shrink: 0;
    min-width: 80px;
    line-height: 1.3;
}

.info-content {
    color: var(--text-primary, #333);
    font-size: 13px;
    text-align: right;
    flex: 1;
    word-break: break-word;
    line-height: 1.3;
}

/* Payment specific styling */
.payment-info .payment-label {
    color: var(--text-secondary, #666);
    font-size: 12px;
    font-weight: 500;
}

/* Level specific styling */
.level-info .level-label {
    color: var(--text-secondary, #666);
    font-size: 12px;
    font-weight: 500;
}

/* Infinite Scroll Styles */
.infinite-scroll-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24px;
    gap: 12px;
}

.loading-spinner .mdc-circular-progress {
    width: 32px;
    height: 32px;
    color: var(--primary-color, #4CAF50);
}

.loading-text {
    color: var(--text-secondary, #666);
    font-size: 14px;
}

.end-of-list {
    display: flex;
    justify-content: center;
    padding: 32px 16px;
}

.end-of-list-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary, #666);
}

.end-of-list-content .material-icons {
    font-size: 32px;
    color: var(--primary-color, #4CAF50);
}

.end-of-list-text {
    font-size: 14px;
    font-weight: 500;
}

.loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 48px 16px;
    color: var(--text-secondary, #666);
    font-size: 14px;
}
</style>

<script>
// Infinite scroll variables - use window object to avoid redeclaration
window.activeStudentsInfiniteScroll = window.activeStudentsInfiniteScroll || {
    currentPage: 2,
    isLoading: false,
    hasMoreData: true,
    currentFilter: '{{ request.GET.filter_by|default:"all" }}'
};

// Initialize mobile search when this template loads
document.addEventListener('DOMContentLoaded', function() {
    // Ensure mobile search is initialized
    if (typeof initializeMobileSearch === 'function') {
        initializeMobileSearch();
    }

    // Initialize infinite scroll
    initializeInfiniteScroll();
});

// Also initialize when HTMX loads this content
document.body.addEventListener('htmx:afterSwap', function(event) {
    // Check if this swap included the mobile search button
    if (event.detail.target.querySelector && event.detail.target.querySelector('#mobile-search-btn')) {
        if (typeof initializeMobileSearch === 'function') {
            initializeMobileSearch();
        }
    }

    // Reinitialize infinite scroll if content area was swapped
    if (event.detail.target.id === 'content-area') {
        window.activeStudentsInfiniteScroll.currentPage = 2;
        window.activeStudentsInfiniteScroll.isLoading = false;
        window.activeStudentsInfiniteScroll.hasMoreData = true;
        window.activeStudentsInfiniteScroll.currentFilter = new URLSearchParams(window.location.search).get('filter_by') || 'all';
        initializeInfiniteScroll();
    }
});

function initializeInfiniteScroll() {
    const contentArea = document.getElementById('content-area');

    // Remove existing scroll listeners
    if (contentArea) {
        contentArea.removeEventListener('scroll', throttledHandleScroll);
    }
    window.removeEventListener('scroll', throttledHandleWindowScroll);

    // Add throttled scroll listeners for better performance
    if (contentArea) {
        contentArea.addEventListener('scroll', throttledHandleScroll, { passive: true });
    }
    window.addEventListener('scroll', throttledHandleWindowScroll, { passive: true });

    // Also add touch events for mobile
    if (contentArea) {
        contentArea.addEventListener('touchmove', throttledHandleScroll, { passive: true });
    }
    document.addEventListener('touchmove', throttledHandleWindowScroll, { passive: true });
}

// Throttle function to limit how often scroll events fire
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

function handleScroll(event) {
    if (window.activeStudentsInfiniteScroll.isLoading || !window.activeStudentsInfiniteScroll.hasMoreData) return;

    const target = event.target;
    const scrollTop = target.scrollTop;
    const scrollHeight = target.scrollHeight;
    const clientHeight = target.clientHeight;

    // More aggressive trigger - load when user is 600px from bottom or 80% scrolled
    const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);
    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

    if (distanceFromBottom <= 600 || scrollPercentage >= 0.8) {
        loadMoreStudents();
    }
}

function handleWindowScroll() {
    if (window.activeStudentsInfiniteScroll.isLoading || !window.activeStudentsInfiniteScroll.hasMoreData) return;

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight;
    const clientHeight = window.innerHeight;

    // More aggressive trigger - load when user is 600px from bottom or 80% scrolled
    const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);
    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

    if (distanceFromBottom <= 600 || scrollPercentage >= 0.8) {
        loadMoreStudents();
    }
}

// Create throttled versions of scroll handlers
const throttledHandleScroll = throttle(handleScroll, 100);
const throttledHandleWindowScroll = throttle(handleWindowScroll, 100);

function loadMoreStudents() {
    if (window.activeStudentsInfiniteScroll.isLoading || !window.activeStudentsInfiniteScroll.hasMoreData) return;

    window.activeStudentsInfiniteScroll.isLoading = true;

    // Show loading indicator
    const loadingIndicator = document.getElementById('infinite-scroll-loading');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'flex';
    }

    // Build URL with current filter
    const url = new URL(window.location.href);
    url.searchParams.set('infinite_scroll', 'true');
    url.searchParams.set('page', window.activeStudentsInfiniteScroll.currentPage);
    url.searchParams.set('filter_by', window.activeStudentsInfiniteScroll.currentFilter);

    fetch(url.toString())
        .then(response => response.json())
        .then(data => {
            // Hide loading indicator
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }

            if (data.html && data.html.trim()) {
                // Append new items to the list
                const studentsList = document.getElementById('students-list');
                if (studentsList) {
                    studentsList.insertAdjacentHTML('beforeend', data.html);

                    // Reinitialize any components for new items
                    if (typeof initializeStudentCardHandlers === 'function') {
                        initializeStudentCardHandlers();
                    }

                    // Initialize ripple effects for new items
                    initializeRippleEffects();
                }

                // Update page counter
                window.activeStudentsInfiniteScroll.currentPage = data.page;
                window.activeStudentsInfiniteScroll.hasMoreData = data.has_more;
            } else {
                window.activeStudentsInfiniteScroll.hasMoreData = false;
            }

            // Show end of list indicator if no more data
            if (!window.activeStudentsInfiniteScroll.hasMoreData) {
                const endOfList = document.getElementById('end-of-list');
                if (endOfList) {
                    endOfList.style.display = 'flex';
                }
            }

            window.activeStudentsInfiniteScroll.isLoading = false;
        })
        .catch(error => {
            console.error('Error loading more students:', error);

            // Hide loading indicator
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }

            window.activeStudentsInfiniteScroll.isLoading = false;
        });
}

function changeFilter(filterValue) {
    // Update active chip
    document.querySelectorAll('.quick-filter-chip').forEach(chip => {
        chip.classList.remove('active');
    });
    document.querySelector(`[data-filter="${filterValue}"]`).classList.add('active');

    // Update URL
    const url = new URL(window.location.href);
    if (filterValue === 'all') {
        url.searchParams.delete('filter_by');
    } else {
        url.searchParams.set('filter_by', filterValue);
    }
    window.history.pushState({}, '', url.toString());

    // Reset infinite scroll state
    window.activeStudentsInfiniteScroll.currentPage = 2;
    window.activeStudentsInfiniteScroll.isLoading = false;
    window.activeStudentsInfiniteScroll.hasMoreData = true;
    window.activeStudentsInfiniteScroll.currentFilter = filterValue;

    // Hide end of list indicator
    const endOfList = document.getElementById('end-of-list');
    if (endOfList) {
        endOfList.style.display = 'none';
    }

    // Load new filtered content
    loadFilteredContent(filterValue);
}

function loadFilteredContent(filterValue) {
    // Show loading state
    const studentsList = document.getElementById('students-list');
    if (studentsList) {
        studentsList.innerHTML = '<div class="loading-placeholder">Chargement...</div>';
    }

    // Build URL for first page with filter
    const url = new URL(window.location.href);
    url.searchParams.set('filter_by', filterValue);

    // Use HTMX to load filtered content
    htmx.ajax('GET', url.toString(), {
        target: '#content-area',
        swap: 'innerHTML'
    });
}

// Initialize ripple effects
function initializeRippleEffects() {
    const rippleElements = document.querySelectorAll('.mdc-ripple-surface:not(.mdc-ripple-upgraded)');
    rippleElements.forEach(element => {
        if (typeof mdc !== 'undefined' && mdc.ripple) {
            mdc.ripple.MDCRipple.attachTo(element);
        }
    });
}

// Immediate initialization for current page
if (typeof initializeMobileSearch === 'function') {
    initializeMobileSearch();
}
if (typeof initializeBottomSheet === 'function') {
    initializeBottomSheet();
}
if (typeof initializeFloatingActionButtons === 'function') {
    initializeFloatingActionButtons();
}
if (typeof initializeStudentCardHandlers === 'function') {
    initializeStudentCardHandlers();
}

// Initialize ripple effects
initializeRippleEffects();
</script>