"""
Demo forms for testing MDC template tags
"""

from django import forms
from django.core.validators import EmailValidator


class MDCDemoForm(forms.Form):
    """Demo form showcasing all MDC field types"""
    
    # Text fields
    first_name = forms.CharField(
        label='First Name',
        max_length=100,
        required=True,
        help_text='Enter your first name'
    )
    
    last_name = forms.CharField(
        label='Last Name',
        max_length=100,
        required=True
    )
    
    email = forms.EmailField(
        label='Email Address',
        required=True,
        validators=[EmailValidator()]
    )
    
    phone = forms.CharField(
        label='Phone Number',
        max_length=15,
        required=False
    )
    
    age = forms.IntegerField(
        label='Age',
        min_value=1,
        max_value=120,
        required=False
    )
    
    birth_date = forms.DateField(
        label='Birth Date',
        required=False,
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    
    website = forms.URLField(
        label='Website',
        required=False
    )
    
    # Select fields
    GENDER_CHOICES = [
        ('', '--------'),
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    ]
    
    gender = forms.ChoiceField(
        label='Gender',
        choices=GENDER_CHOICES,
        required=True
    )
    
    COUNTRY_CHOICES = [
        ('', '--------'),
        ('US', 'United States'),
        ('CA', 'Canada'),
        ('UK', 'United Kingdom'),
        ('FR', 'France'),
        ('DE', 'Germany'),
        ('JP', 'Japan'),
        ('AU', 'Australia'),
    ]
    
    country = forms.ChoiceField(
        label='Country',
        choices=COUNTRY_CHOICES,
        required=False
    )
    
    # Textarea fields
    bio = forms.CharField(
        label='Biography',
        widget=forms.Textarea(attrs={'rows': 4}),
        required=False,
        help_text='Tell us about yourself'
    )
    
    notes = forms.CharField(
        label='Additional Notes',
        widget=forms.Textarea(attrs={'rows': 6}),
        required=False
    )
    
    # Checkbox fields
    newsletter = forms.BooleanField(
        label='Subscribe to Newsletter',
        required=False,
        help_text='Receive updates and news'
    )
    
    terms_accepted = forms.BooleanField(
        label='I accept the Terms and Conditions',
        required=True
    )
    
    # HTMX demo fields
    student_id = forms.CharField(
        label='Student ID',
        max_length=10,
        required=False,
        help_text='Enter student ID to load info'
    )
    
    LEVEL_CHOICES = [
        ('', '--------'),
        ('1', 'Level 1'),
        ('2', 'Level 2'),
        ('3', 'Level 3'),
        ('4', 'Level 4'),
        ('5', 'Level 5'),
    ]
    
    level = forms.ChoiceField(
        label='Level',
        choices=LEVEL_CHOICES,
        required=False,
        help_text='Select level to load fees'
    )
    
    # Error demo fields
    required_field = forms.CharField(
        label='Required Field',
        required=True,
        help_text='This field is required'
    )
    
    email_field = forms.EmailField(
        label='Email Validation',
        required=True,
        help_text='Must be a valid email address'
    )
    
    def clean_student_id(self):
        """Custom validation for student ID"""
        student_id = self.cleaned_data.get('student_id')
        if student_id and len(student_id) < 3:
            raise forms.ValidationError('Student ID must be at least 3 characters long')
        return student_id
    
    def clean(self):
        """Form-wide validation"""
        cleaned_data = super().clean()
        first_name = cleaned_data.get('first_name')
        last_name = cleaned_data.get('last_name')
        
        if first_name and last_name and first_name.lower() == last_name.lower():
            raise forms.ValidationError('First name and last name cannot be the same')
        
        return cleaned_data


class SimpleStudentForm(forms.Form):
    """Simplified student form for testing"""
    
    student_id = forms.CharField(
        label='Student ID',
        max_length=9,
        required=True
    )
    
    first_name = forms.CharField(
        label='First Name',
        max_length=100,
        required=True
    )
    
    last_name = forms.CharField(
        label='Last Name', 
        max_length=100,
        required=True
    )
    
    email = forms.EmailField(
        label='Email',
        required=False
    )
    
    GENDER_CHOICES = [
        ('', '--------'),
        ('M', 'Male'),
        ('F', 'Female'),
    ]
    
    gender = forms.ChoiceField(
        label='Gender',
        choices=GENDER_CHOICES,
        required=True
    )
    
    birth_date = forms.DateField(
        label='Birth Date',
        required=True,
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    
    notes = forms.CharField(
        label='Notes',
        widget=forms.Textarea(attrs={'rows': 3}),
        required=False
    )
    
    active = forms.BooleanField(
        label='Active Student',
        required=False,
        initial=True
    )
