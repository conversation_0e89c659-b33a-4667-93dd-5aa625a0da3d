{% extends 'partials/modal.html' %}
{% load i18n %}
{% load widget_tweaks %}

{% block modal_title %}
<span>Inscription</span> 
<span style="position: absolute; right: 40px;" class="badge badge-pill {% if not enrollment.active %} badge-warning {% else %} badge-success {% endif %}">
    {% if not enrollment.active %} Elève non inscrit {% else %} Elève inscrit {% endif %}
</span>
{% endblock %}

{% block modal_body %}
<div class="row">
        <!-- Student Information Column -->
        <div class="col-md-5 border-right">
            <h5 class="text-muted"><span data-feather="user" class="feather-16"></span> Informations sur l'élève</h5>
            <div class="form-group">
                <label for="{{ student_form.student_id.id_for_label }}">{{ student_form.student_id.label }}</label>
                {% render_field student_form.student_id onkeyup='this.value = this.value.toUpperCase();' class='form-control' %}
            </div>
            <section class="form-row">
                <div class="form-group col-xl-4">
                    <label for="{{ student_form.last_name.id_for_label }}" class=" font-weight-bold">*{{ student_form.last_name.label }}</label>
                    {% render_field student_form.last_name class='form-control' onkeyup='this.value = this.value.toUpperCase();' %}
                </div>
                {% if user.school.education == 'F' %}
                <div class="form-group col-xl-8">
                    <label for="{{ student_form.first_name.id_for_label }}" class=" font-weight-bold">* {{ student_form.first_name.label }}</label>
                    {% render_field student_form.first_name onkeyup='this.value = this.value.toUpperCase();' class='form-control' %}
                </div>
                {% else %}
                <div class="form-group col-xl-8">
                    <label for="{{ student_form.first_name.id_for_label }}" class=" font-weight-bold">* {{ student_form.first_name.label }}</label>
                    {% render_field student_form.first_name onkeyup='this.value = this.value.toUpperCase();' class='form-control' hx-get='/transliterate/?field=full_name_ar' hx-target='#id_full_name_ar' hx-trigger='keyup delay:3s' hx-swap='outerHTML' %}
                </div>
                {% endif %}
            </section>
            <section class="form-row">
                <div class="form-group col-4">
                    <label for="{{ student_form.birth_day.id_for_label }}">{{ student_form.birth_day.label }}</label>
                    {% render_field student_form.birth_day class='form-control' %}
                </div>
                <div class="form-group col-4">
                    <label for="{{ student_form.birth_month.id_for_label }}">{{ student_form.birth_month.label }}</label>
                    {% render_field student_form.birth_month class='form-control' %}
                </div>
                <div class="form-group col-4">
                    <label for="{{ student_form.birth_year.id_for_label }}">{{ student_form.birth_year.label }}</label>
                    {% render_field student_form.birth_year class='form-control' max=max_year %}
                </div>
            </section>
            <section class="form-row">
                {% if user.school.education == 'F' %}
                <div class="form-group col-4">
                    <label for="{{ student_form.birth_place.id_for_label }}">{{ student_form.birth_place.label }}</label>
                    {% render_field student_form.birth_place onkeyup='this.value = this.value.toUpperCase();' class='form-control' %}
                </div>
                {% else %}
                <div class="form-group col-4">
                    <label for="{{ student_form.birth_place.id_for_label }}">{{ student_form.birth_place.label }}</label>
                    {% render_field student_form.birth_place onkeyup='this.value = this.value.toUpperCase();' class='form-control' hx-get='/transliterate/?field=birth_place_ar' hx-target='#id_birth_place_ar' hx-trigger='keyup delay:3s' hx-swap='outerHTML' %}
                </div>
                {% endif %}
                <div class="form-group col-4">
                    <label for="{{ student_form.gender.id_for_label }}" class="font-weight-bold">* {{ student_form.gender.label }}</label>
                    {% render_field student_form.gender class='form-control' %}
                </div>
                <div class="form-group col-4">
                    <label for="{{ student_form.nationality.id_for_label }}" class="font-weight-bold">* {{ student_form.nationality.label }}</label>
                    {% render_field student_form.nationality class='form-control' %}
                </div>
            </section>
            <section class="form-row">
                <div class="form-group col-8">
                    <label for="{{ student_form.full_name_ar.id_for_label }}">{{ student_form.full_name_ar.label }}</label>
                    {% render_field student_form.full_name_ar class='form-control' hx-swap='outerHTML' %}
                </div>
                <div class="form-group col-4">
                    <label for="{{ student_form.birth_place_ar.id_for_label }}">Lieu Naiss. Arabe</label>
                    {% render_field student_form.birth_place_ar class='form-control' hx-swap='outerHTML' %}
                </div>
            </section>
            <section class="form-row">
                <div class="form-group col-8">
                    <label for="{{ level_form.qualite.id_for_label }}">{{ level_form.qualite.label }}</label>
                    {% render_field level_form.qualite class='form-control' %}
                </div>
                <div class="form-group col-4">
                    <label for="{{ level_form.status.id_for_label }}">{{ level_form.status.label }}</label>
                    {% render_field level_form.status|add_class:"form-control"|attr:"hx-get:/versements/frais_scolarite/"|attr:"hx-target:#id_2-year_fees_container" hx-swap="outerHTML" %}
                </div>
            </section>
        </div>

        <!-- Parent Information Column -->
        <div class="col-md-3 border-right border-left">
            <h5 class="text-muted"><span data-feather="users" class="feather-16"></span> Infos parents et Photo</h5>
            <div class="form-group">
                <label for="{{ parents_form.father.id_for_label }}">{{ parents_form.father.label }}</label>
                {% render_field parents_form.father onkeyup='this.value = this.value.toUpperCase();' class='form-control' %}
            </div>
            <div class="form-group">
                <label for="{{ parents_form.mother.id_for_label }}">{{ parents_form.mother.label }}</label>
                {% render_field parents_form.mother onkeyup='this.value = this.value.toUpperCase();' class='form-control' %}
            </div>
            <div class="form-group">
                <label for="{{ parents_form.father_phone.id_for_label }}">Contact du parent ou du tuteur</label>
                {% render_field parents_form.father_phone class='form-control' %}
            </div>
            
            <div class="form-group text-center">
                {% if enrollment and enrollment.student.photo %}
                    <img alt="Photo de l'élève" height="100px" width="90px" 
                    src="{{ enrollment.student.photo.url }}" id="photo" loading="lazy" class="rounded">
                {% else %}
                    <img alt="Photo de l'élève" height="100px" width="90px" 
                        src="{{ blank_photo }}" id="photo" loading="lazy" class="rounded">
                {% endif %}
            </div>
            <div class="form-group">
                <input type="file" class="form-control" name="3-photo" 
                id="{{ files_form.photo.id_for_label }}"
                accept=".jpg, .png, .jpeg" onchange="
                    const selectedImage = document.getElementById('photo');
                    const reader = new FileReader();

                    if (this.files && this.files[0]) {
                        reader.onload = function(e) {
                            selectedImage.src = e.target.result;
                        };
                        reader.readAsDataURL(this.files[0]);
                    } else {
                        selectedImage.src = '/static/img/avatar.jpg';
                    }
                ">    
            </div>
            <div class="form-group" style="display: none;">
                <input type="file" class="form-control" name="3-certificate_img" 
                id="{{ files_form.certificate_img.id_for_label }}"
                accept=".jpg, .png, .jpeg">    
            </div>
        </div>

        <!-- Registration Information Column -->
        <div class="col-md-4 border-left">
            <h5 class="text-muted"><span data-feather="chevrons-right" class="feather-16"></span> Fréquentation {{ active_year }}</h5>
            <section class="form-row">
                {% if user.school.subschool_set.count > 1 %}
                <div class="form-group col-12">
                    <label for="{{ level_form.subschool.id_for_label }}">Ecole</label>
                    {% render_field level_form.subschool class='form-control' %}
                </div>
                {% endif %}
                <div class="form-group col-6 d-none">
                    <label for="{{ level_form.generic_level_fr.id_for_label }}" class=" font-weight-bold"> * {{ level_form.generic_level_fr.label }}</label>
                    {% render_field level_form.generic_level_fr|add_class:"form-control"|attr:"hx-get:/versements/frais_scolarite/"|attr:"hx-target:#id_2-year_fees" hx-swap="outerHTML" %}
                </div>
                <div class="form-group col-6 d-none">
                    <label for="{{ level_form.generic_level_ar.id_for_label }}" class="text-muted">{{ level_form.generic_level_ar.label }}</label>
                    {% render_field level_form.generic_level_ar|add_class:"form-control"|attr:"hx-get:/versements/frais_scolarite/"|attr:"hx-target:#id_2-year_fees" hx-swap="outerHTML" %}

                </div>
                
                <div class="form-group col-6 {% if level_form.level_fr.help_text == 'd-none' %} d-none {% endif %}">
                    <label for="{{ level_form.level_fr.id_for_label }}" class=" font-weight-bold">* {{ level_form.level_fr.label }}</label>
                    {% render_field level_form.level_fr|add_class:"form-control"|attr:"hx-get:/versements/frais_scolarite/"|attr:"hx-target:#id_2-year_fees" hx-swap="outerHTML" %}
                    {% if level_form.generic_level_fr.errors %}
                        <div class="invalid-feedback d-block">
                            {{ level_form.generic_level_fr.errors.0 }}
                        </div>
                    {% endif %}
                    {% if level_form.level_fr.errors %}
                        <div class="invalid-feedback d-block">
                            {{ level_form.level_fr.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                <div class="form-group col-6 {% if level_form.level_ar.help_text == 'd-none' %} d-none {% endif %}">
                    <label for="{{ level_form.level_ar.id_for_label }}" class="text-muted">{{ level_form.level_ar.label }}</label>
                    {% render_field level_form.level_ar|add_class:"form-control"|attr:"hx-get:/versements/frais_scolarite/"|attr:"hx-target:#id_2-year_fees" hx-swap="outerHTML" %}
                    {% if level_form.generic_level_ar.errors %}
                        <div class="invalid-feedback d-block">
                            {{ level_form.generic_level_ar.errors.0 }}
                        </div>
                    {% endif %}
                    {% if level_form.level_ar.errors %}
                        <div class="invalid-feedback d-block">
                            {{ level_form.level_ar.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </section>
            {% if perms.school.add_payment %} 
            <h5 class="text-muted" data-toggle="tooltip" title="Défini le montant total à payer par rubrique pour l'année scolaire"><span data-feather="dollar-sign" class="feather-16"></span> Montant à payer par rubrique</h5>
            {% endif %}
            <section class="form-row {% if not perms.school.add_payment %} d-none {% endif %}">
                <div class="form-group col-4">
                    <label for="{{ level_form.enrollment_fees.id_for_label }}" class="text-muted">{{ level_form.enrollment_fees.label }}</label>
                    {% render_field level_form.enrollment_fees class='form-control' %}
                </div>
                <div class="form-group col-4">
                    <label for="{{ level_form.year_fees.id_for_label }}" class="text-muted">{{ level_form.year_fees.label }}</label>
                    {% render_field level_form.year_fees class='form-control' %}
                </div>
                <div class="form-group col-4">
                    <label for="{{ level_form.annexe_fees.id_for_label }}" class="text-muted">{{ level_form.annexe_fees.label }}</label>
                    {% render_field level_form.annexe_fees class='form-control' %}
                </div>
            </section>
            {% if perms.school.add_payment %}
                {% include 'partials/payment/payments_component.html' with form=level_form hide_agent=True %}
            {% endif %}
        </div>  
    </div>
{% endblock %}

{% block modal_footer %}
<span style="position: absolute; left: 10px;">
    <span data-feather="info" class="align-middle feather-16"></span> 
    <span id="info" hx-swap="outerHTML">Les champs en <span class="font-weight-bold">gras</span> et précédés de (*) sont obligatoires.</span> </span> 
    <button type="submit" class="btn btn-success" id="submit-btn">
        <span class="feather-16 align-middle" data-feather="check-circle"></span> {% trans 'Valider' %}
    </button>
{% endblock %}

{% block js %}
<script>
    if(typeof(feather) !== undefined) feather.replace();

    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    })
    
</script>
{% endblock %}