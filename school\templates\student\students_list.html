{% load humanize %}
<div class="content-header" id="content-header">
    <span class="material-icons">arrow_back</span>
    <h2 class="page-title">
        <span>Liste des élèves</span>
    </h2>
    <div class="actions">
        <button class="mdc-button mdc-button--raised add-btn" id="add-student-btn"
                hx-get="{% url 'school:student_add' %}"
                hx-target="#modal-content"
                hx-swap="innerHTML">
            <span class="mdc-button__ripple"></span>
            <span class="material-icons" style="pointer-events: none;">add</span>
            <span class="mdc-button__label">Ajouter un élève</span>
        </button>
        <span class="material-icons hidden" title="Exporter vers Excel">description</span>
        <span class="material-icons hidden" title="Importer des données">file_upload</span>
        <span class="material-icons hidden" title="Changer de vue">view_module</span>
        <span class="material-icons search-icon-mobile" title="Rechercher" id="mobile-search-btn">search</span>
        <span class="material-icons hidden-on-large-screens" title="Filtrer" id="filter-btn">filter_list</span>
        <span class="material-icons hidden-on-large-screens" title="Vue liste / grille" id="filter-btn" hx-get="{{ request.path }}">list</span>

    </div>
</div>
<div class="content-area" id="content-area" hx-include="*">
            <!-- Quick Filter Chips -->
        <div class="quick-filter-chips" x-data="{activeChip: 'all'}" hx-target="#main-content">
            <button class="quick-filter-chip {% if not 'filter_by' in request.GET or not request.GET.filter_by or request.GET.filter_by == 'all' %} active {% endif %}"
                    hx-get="{{request.path }}?filter_by=all" hx-push-url="true"
                    onclick="createRipple(event, this)">
                <span class="material-icons">group</span>
                <span>Tous</span>
            </button>
            <button class="quick-filter-chip {% if request.GET.filter_by == 'paid' %} active {% endif %}"
                    hx-get="{{request.path }}?filter_by=paid" hx-push-url="true"
                    onclick="createRipple(event, this)">
                <span class="material-icons">paid</span>
                <span>Payé</span>
            </button>
            <button class="quick-filter-chip {% if request.GET.filter_by == 'unpaid' %} active {% endif %}"
                    hx-get="{{request.path }}?filter_by=unpaid" hx-push-url="true"
                    onclick="createRipple(event, this)">
                <span class="material-icons">money_off</span>
                <span>Non payé</span>
            </button>
            <button class="quick-filter-chip {% if request.GET.filter_by == 'full_paid' %} active {% endif %}"
                    hx-get="{{request.path }}?filter_by=full_paid" hx-push-url="true"
                    onclick="createRipple(event, this)">
                <span class="material-icons">check_circle</span>
                <span>Soldé</span>
            </button>
            <button class="quick-filter-chip {% if request.GET.filter_by == 'today' %} active {% endif %}"
                    hx-get="{{request.path }}?filter_by=today" hx-push-url="true"
                    onclick="createRipple(event, this)">
                <span class="material-icons">today</span>
                <span>Aujourd'hui</span>
            </button>
            <button class="quick-filter-chip {% if request.GET.filter_by == 'this_week' %} active {% endif %}"
                    hx-get="{{request.path }}?filter_by=this_week" hx-push-url="true"
                    onclick="createRipple(event, this)">
                <span class="material-icons">date_range</span>
                <span>Cette semaine</span>
            </button>
            <button class="quick-filter-chip {% if request.GET.filter_by == 'this_month' %} active {% endif %}"
                    hx-get="{{request.path }}?filter_by=this_month" hx-push-url="true"
                    onclick="createRipple(event, this)">
                <span class="material-icons">calendar_month</span>
                <span>Ce mois</span>
            </button>
        </div>
    <div class="data-table-container">
        <!-- Table Controls -->

        <div class="table-controls hidden-on-small-screens"
             {% if 'filter_by' in request.GET %} hx-vals='{"filter_by": "{{ request.GET.filter_by }}"}' {% endif %}
             {% if request.GET.generic_level_fr or request.GET.level_fr or request.GET.generic_level_ar or request.GET.level_ar %}
             hx-vals='{"generic_level_fr": "{{ request.GET.generic_level_fr|default:'' }}", "level_fr": "{{ request.GET.level_fr|default:'' }}", "generic_level_ar": "{{ request.GET.generic_level_ar|default:'' }}", "level_ar": "{{ request.GET.level_ar|default:'' }}"}'
             {% endif %}>
            <!-- Search and Filter Controls -->
            <div class="filter-controls">
                <!-- Search Filter -->
                <div class="filter-item">
                    <span class="filter-label">Rechercher</span>
                    <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon mdc-small-input mdc-search-input">
                        <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">search</span>
                        <input type="text" class="mdc-text-field__input" id="table-search"
                            placeholder="Rechercher des élèves..."
                            name="search" hx-get="{{ request.path }}" hx-target="#content-area"
                            hx-trigger="keyup delay:1s" value="{{ search|default:''}}"
                            hx-include="[name='search'], [name='generic_level_fr'], [name='level_fr'], [name='generic_level_ar'], [name='level_ar']">
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch"></div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>
                <!-- Generic Level FR Filter -->
                <div class="filter-item">
                    <span class="filter-label">Niveau</span>
                    <div class="mdc-select mdc-select--outlined mdc-small-input" id="generic-level-fr-select">
                        <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text">
                                    {% if filter_form.generic_level_fr.value %}
                                        {% for choice in filter_form.generic_level_fr.field.queryset %}
                                            {% if choice.id|stringformat:'s' == filter_form.generic_level_fr.value %}{{ choice.short_name }}{% endif %}
                                        {% endfor %}
                                    {% else %}Tous{% endif %}
                                </span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list" role="listbox">
                                <li class="mdc-deprecated-list-item {% if not filter_form.generic_level_fr.value %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="" role="option" {% if not filter_form.generic_level_fr.value %}aria-selected="true"{% endif %}>
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">Tous</span>
                                </li>
                                {% for choice in filter_form.generic_level_fr.field.queryset %}
                                <li class="mdc-deprecated-list-item {% if filter_form.generic_level_fr.value == choice.id|stringformat:'s' %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="{{ choice.id }}" role="option" {% if filter_form.generic_level_fr.value == choice.id|stringformat:'s' %}aria-selected="true"{% endif %}>
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">{{ choice.short_name }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <input type="hidden" name="generic_level_fr" value="{{ filter_form.generic_level_fr.value|default:'' }}"
                               hx-get="{{ request.path }}" hx-target="#content-area" hx-trigger="change"
                               hx-include="[name='search'], [name='generic_level_fr'], [name='level_fr'], [name='generic_level_ar'], [name='level_ar']">
                    </div>
                </div>

                <!-- Level FR Filter -->
                <div class="filter-item">
                    <span class="filter-label">Classe</span>
                    <div class="mdc-select mdc-select--outlined mdc-small-input" id="level-fr-select">
                        <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text">
                                    {% if filter_form.level_fr.value %}
                                        {% for choice in filter_form.level_fr.field.queryset %}
                                            {% if choice.id|stringformat:'s' == filter_form.level_fr.value %}{{ choice.number }}{% endif %}
                                        {% endfor %}
                                    {% else %}Tous{% endif %}
                                </span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list" role="listbox">
                                <li class="mdc-deprecated-list-item {% if not filter_form.level_fr.value %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="" role="option" {% if not filter_form.level_fr.value %}aria-selected="true"{% endif %}>
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">Tous</span>
                                </li>
                                {% for choice in filter_form.level_fr.field.queryset %}
                                <li class="mdc-deprecated-list-item {% if filter_form.level_fr.value == choice.id|stringformat:'s' %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="{{ choice.id }}" role="option" {% if filter_form.level_fr.value == choice.id|stringformat:'s' %}aria-selected="true"{% endif %}>
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">{{ choice.number }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <input type="hidden" name="level_fr" value="{{ filter_form.level_fr.value|default:'' }}"
                               hx-get="{{ request.path }}" hx-target="#content-area" hx-trigger="change"
                               hx-include="[name='search'], [name='generic_level_fr'], [name='level_fr'], [name='generic_level_ar'], [name='level_ar']">
                    </div>
                </div>

                {% if is_arabic_school %}
                <!-- Generic Level AR Filter -->
                <div class="filter-item">
                    <span class="filter-label">Niveau arabe</span>
                    <div class="mdc-select mdc-select--outlined mdc-small-input" id="generic-level-ar-select">
                        <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text">
                                    {% if filter_form.generic_level_ar.value %}
                                        {% for choice in filter_form.generic_level_ar.field.queryset %}
                                            {% if choice.id|stringformat:'s' == filter_form.generic_level_ar.value %}{{ choice.short_name }}{% endif %}
                                        {% endfor %}
                                    {% else %}Tous{% endif %}
                                </span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list" role="listbox">
                                <li class="mdc-deprecated-list-item {% if not filter_form.generic_level_ar.value %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="" role="option" {% if not filter_form.generic_level_ar.value %}aria-selected="true"{% endif %}>
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">Tous</span>
                                </li>
                                {% for choice in filter_form.generic_level_ar.field.queryset %}
                                <li class="mdc-deprecated-list-item {% if filter_form.generic_level_ar.value == choice.id|stringformat:'s' %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="{{ choice.id }}" role="option" {% if filter_form.generic_level_ar.value == choice.id|stringformat:'s' %}aria-selected="true"{% endif %}>
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">{{ choice.short_name }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <input type="hidden" name="generic_level_ar" value="{{ filter_form.generic_level_ar.value|default:'' }}"
                               hx-get="{{ request.path }}" hx-target="#content-area" hx-trigger="change"
                               hx-include="[name='search'], [name='generic_level_fr'], [name='level_fr'], [name='generic_level_ar'], [name='level_ar']">
                    </div>
                </div>

                <!-- Level AR Filter -->
                <div class="filter-item">
                    <span class="filter-label">Classe arabe</span>
                    <div class="mdc-select mdc-select--outlined mdc-small-input" id="level-ar-select">
                        <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text">
                                    {% if filter_form.level_ar.value %}
                                        {% for choice in filter_form.level_ar.field.queryset %}
                                            {% if choice.id|stringformat:'s' == filter_form.level_ar.value %}{{ choice.number }}{% endif %}
                                        {% endfor %}
                                    {% else %}Tous{% endif %}
                                </span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list" role="listbox">
                                <li class="mdc-deprecated-list-item {% if not filter_form.level_ar.value %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="" role="option" {% if not filter_form.level_ar.value %}aria-selected="true"{% endif %}>
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">Tous</span>
                                </li>
                                {% for choice in filter_form.level_ar.field.queryset %}
                                <li class="mdc-deprecated-list-item {% if filter_form.level_ar.value == choice.id|stringformat:'s' %}mdc-deprecated-list-item--selected{% endif %}"
                                    data-value="{{ choice.id }}" role="option" {% if filter_form.level_ar.value == choice.id|stringformat:'s' %}aria-selected="true"{% endif %}>
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">{{ choice.number }}</span>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        <input type="hidden" name="level_ar" value="{{ filter_form.level_ar.value|default:'' }}"
                               hx-get="{{ request.path }}" hx-target="#content-area" hx-trigger="change"
                               hx-include="[name='search'], [name='generic_level_fr'], [name='level_fr'], [name='generic_level_ar'], [name='level_ar']">
                    </div>
                </div>
                {% endif %}
            </div>
            <!-- <div class="per-page-container">
                <span class="per-page-label">Lignes par page:</span>
                <div class="mdc-select mdc-select--outlined mdc-small-input" id="per-page-select">
                    <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false">
                        <span class="mdc-select__selected-text-container">
                            <span class="mdc-select__selected-text">10</span>
                        </span>
                        <span class="mdc-select__dropdown-icon">
                            <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                                <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                            </svg>
                        </span>
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                    <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                        <ul class="mdc-deprecated-list" role="listbox">
                            <li class="mdc-deprecated-list-item" data-value="5" role="option">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">5</span>
                            </li>
                            <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="10" role="option" aria-selected="true">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">10</span>
                            </li>
                            <li class="mdc-deprecated-list-item" data-value="25" role="option">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">25</span>
                            </li>
                            <li class="mdc-deprecated-list-item" data-value="50" role="option">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">50</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div> -->
        </div>

        <!-- Active Filter Chips Container -->
        {% if request.GET.filter and request.GET.filter != 'all' %}
        <div class="filter-chips-container" id="filter-chips-container">
            <div class="filter-chip">
                <span>
                    {% if request.GET.filter == 'paid' %}Payé
                    {% elif request.GET.filter == 'no-payments' %}Aucun Paiement
                    {% elif request.GET.filter == 'all-paid' %}Soldé
                    {% elif request.GET.filter == 'enrolled-today' %}Aujourd'hui
                    {% elif request.GET.filter == 'this-week' %}Cette semaine
                    {% elif request.GET.filter == 'this-month' %}Ce mois
                    {% else %}{{ request.GET.filter|title }}
                    {% endif %}
                </span>
                <a href="?" class="filter-chip-remove" title="Supprimer le filtre">
                    <span class="material-icons">close</span>
                </a>
            </div>
        </div>
        {% endif %}

        <!-- MDC Data Table -->
        <div class="mdc-data-table">
            <div class="mdc-data-table__table-container">
                <table class="mdc-data-table__table" aria-label="Liste des élèves">
                    <thead>
                        <tr class="mdc-data-table__header-row">
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" role="columnheader" scope="col">
                                <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" id="select-all-checkbox" aria-label="Sélectionner toutes les lignes">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Photo</th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Nom et Prénoms</th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Matricule</th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Classe</th>
                            {% if is_arabic_school %}
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Arabe</th>
                            {% endif %}
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Sexe</th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Statut</th>
                            <th class="mdc-data-table__header-cell numeric-column" role="columnheader" scope="col">Arriéré</th>
                            <th class="mdc-data-table__header-cell numeric-column" role="columnheader" scope="col">À payer</th>
                            <th class="mdc-data-table__header-cell numeric-column" role="columnheader" scope="col">Payé</th>
                            <th class="mdc-data-table__header-cell numeric-column" role="columnheader" scope="col">Reste</th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="mdc-data-table__content" id="table-body">
                        {% for enrollment in enrollments %}
                        <tr class="mdc-data-table__row table-row" data-student-id="{{ enrollment.id }}">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control row-checkbox" aria-label="Sélectionner la ligne">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </td>
                            <td class="mdc-data-table__cell">
                                <div class="student-photo" style="background-image: url('{% if enrollment.student.photo %}{{ enrollment.student.photo.url }}{% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}{{ enrollment.student.government_photo }}{% else %}{{ enrollment.student.blank_photo }}{% endif %}')"></div>
                            </td>
                            <th class="mdc-data-table__cell" scope="row">
                                <div>{{ enrollment.student.get_full_name }}</div>
                                {% if enrollment.student.full_name_ar %}
                                <div class="text-muted small">{{ enrollment.student.full_name_ar }}</div>
                                {% endif %}
                            </th>
                            <td class="mdc-data-table__cell">{{ enrollment.student.student_id|default:'-' }}</td>
                            <td class="mdc-data-table__cell">{{ enrollment.level_fr|default:'-' }}</td>
                            {% if is_arabic_school %}
                            <td class="mdc-data-table__cell">{{ enrollment.level_ar|default:'-' }}</td>
                            {% endif %}
                            <td class="mdc-data-table__cell">{{ enrollment.student.gender }}</td>
                            <td class="mdc-data-table__cell">
                                <span class="status-badge {% if enrollment.active %}status-active{% else %}status-inactive{% endif %}">
                                    {{ enrollment.status|default:'Actif' }}
                                </span>
                            </td>
                            <td class="mdc-data-table__cell numeric-cell amount-negative">{{ enrollment.debt|floatformat:0|default:'-' }}</td>
                            <td class="mdc-data-table__cell numeric-cell">{{ enrollment.amount|floatformat:0|default:'-' }}</td>
                            <td class="mdc-data-table__cell numeric-cell {% if enrollment.paid > 0 %}amount-positive{% endif %}">{{ enrollment.paid|floatformat:0|default:'-' }}</td>
                            <td class="mdc-data-table__cell numeric-cell {% if enrollment.remaining == 0 and enrollment.amount > 0 %}amount-positive{% elif enrollment.remaining > 0 %}amount-warning{% endif %}">
                                {% if enrollment.remaining == 0 and enrollment.amount > 0 %}
                                    Soldé
                                {% else %}
                                    {{ enrollment.remaining|floatformat:0|default:'-' }}
                                {% endif %}
                            </td>
                            <td class="mdc-data-table__cell">
                                <div class="table-actions">
                                    <button class="action-btn edit-btn" title="Modifier l'élève" hx-get="{% url 'school:student_edit' enrollment.id %}?page={{ page }}"
                                        hx-target="#modal-content">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn payment-btn" title="Ajouter/Modifier un paiement">
                                        <span class="material-icons">payments</span>
                                    </button>
                                    <button class="action-btn delete-btn" title="Supprimer l'élève">
                                        <span class="material-icons">delete</span>
                                    </button>
                                    <button class="action-btn more-btn" title="Plus d'options">
                                        <span class="material-icons">more_vert</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell" colspan="13" style="text-align: center; padding: 48px;">
                                <div style="color: #757575;">
                                    <span class="material-icons" style="font-size: 48px; margin-bottom: 16px; display: block;">search_off</span>
                                    Aucun élève trouvé
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Material Design Pagination -->
            {% include 'material_pagination.html' with page_obj=page_obj per_page=per_page include_items='.mdc-text-field__input,.mdc-select' %}
        </div>
    </div>
</div>

<script src="{% load static %}{% static 'material/js/table-row-selection.js' %}"></script>
<script src="{% load static %}{% static 'material/js/floating-actions.js' %}"></script>
<script>
// Function to initialize filter select components
function initializeFilterSelects() {
    const filterSelects = [
        'generic-level-fr-select',
        'level-fr-select',
        'generic-level-ar-select',
        'level-ar-select'
    ];

    filterSelects.forEach(selectId => {
        const selectElement = document.getElementById(selectId);
        if (selectElement && typeof mdc !== 'undefined') {
            const select = new mdc.select.MDCSelect(selectElement);

            // Handle selection change
            select.listen('MDCSelect:change', () => {
                const hiddenInput = selectElement.querySelector('input[type="hidden"]');
                if (hiddenInput) {
                    hiddenInput.value = select.value;
                    // Trigger HTMX request
                    htmx.trigger(hiddenInput, 'change');
                }
            });
        }
    });
}

// Initialize components when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize MDC Select component
    const selectElement = document.getElementById('per-page-select');
    if (selectElement && typeof mdc !== 'undefined') {
        const select = new mdc.select.MDCSelect(selectElement);
    }

    // Initialize MDC Text Field for search
    const searchField = document.querySelector('.mdc-search-input');
    if (searchField && typeof mdc !== 'undefined') {
        const textField = new mdc.textField.MDCTextField(searchField);
    }

    // Initialize filter select components
    initializeFilterSelects();

    // Initialize add student button
    const addStudentBtn = document.getElementById('add-student-btn');
    if (addStudentBtn && typeof mdc !== 'undefined') {
        new mdc.ripple.MDCRipple(addStudentBtn);
    }

    // Initialize search functionality
    const searchInput = document.getElementById('table-search');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            handleSearch(e.target.value);
        });
    }

    // Initialize floating actions for bulk operations
    const floatingActions = new FloatingActions({
        containerId: 'students-floating-actions',
        actions: [
            {
                id: 'export',
                icon: 'download',
                label: 'Exporter',
                type: 'archive',
                tooltip: 'Exporter les élèves sélectionnés',
                handler: function(count, actionId) {
                    handleBulkExport(count);
                }
            },
        ],
        onShow: function(count) {
            console.log(`Floating actions shown for ${count} selected students`);
        },
        onHide: function() {
            console.log('Floating actions hidden');
        }
    });

    // Initialize generic table row selection
    const tableSelection = new TableRowSelection({
        tableSelector: '.mdc-data-table',
        headerCheckboxSelector: '#select-all-checkbox',
        rowCheckboxSelector: '.row-checkbox',
        rowSelector: '.mdc-data-table__row:not(.mdc-data-table__header-row)',
        onSelectionChange: function(selectedRows) {
            console.log('Selection changed:', selectedRows);
            // Show/hide floating actions based on selection
            if (selectedRows.length > 0) {
                floatingActions.show(selectedRows.length);
            } else {
                floatingActions.hide();
            }
        },
        onSelectAll: function(selectedRows) {
            console.log('All rows selected:', selectedRows);
            floatingActions.show(selectedRows.length);
        },
        onDeselectAll: function() {
            console.log('All rows deselected');
            floatingActions.hide();
        }
    });

    // Store references globally for potential external access
    window.studentsTableSelection = tableSelection;
    window.studentsFloatingActions = floatingActions;
});

// Bulk action handlers
function handleBulkEdit(count) {
    const selectedIds = window.getSelectedStudents();
    console.log(`Editing ${count} students:`, selectedIds);

    // Show confirmation or open bulk edit modal
    if (confirm(`Voulez-vous modifier ${count} élève${count > 1 ? 's' : ''} sélectionné${count > 1 ? 's' : ''} ?`)) {
        // Implement bulk edit logic here
        // For example: open a modal with bulk edit form
        alert('Fonctionnalité de modification en lot à implémenter');
    }
}

function handleBulkExport(count) {
    const selectedIds = window.getSelectedStudents();
    console.log(`Exporting ${count} students:`, selectedIds);

    // Implement export logic here
    // For example: generate and download CSV/Excel file
    alert(`Export de ${count} élève${count > 1 ? 's' : ''} en cours...`);
}

function handleBulkArchive(count) {
    const selectedIds = window.getSelectedStudents();
    console.log(`Archiving ${count} students:`, selectedIds);

    if (confirm(`Voulez-vous archiver ${count} élève${count > 1 ? 's' : ''} sélectionné${count > 1 ? 's' : ''} ?`)) {
        // Implement bulk archive logic here
        alert('Fonctionnalité d\'archivage en lot à implémenter');
    }
}

function handleBulkDelete(count) {
    const selectedIds = window.getSelectedStudents();
    console.log(`Deleting ${count} students:`, selectedIds);

    if (confirm(`Êtes-vous sûr de vouloir supprimer ${count} élève${count > 1 ? 's' : ''} sélectionné${count > 1 ? 's' : ''} ? Cette action ne peut pas être annulée.`)) {
        // Implement bulk delete logic here
        alert('Fonctionnalité de suppression en lot à implémenter');
    }
}

// Public API for external use
window.getSelectedStudents = function() {
    return window.studentsTableSelection ? window.studentsTableSelection.getSelectedRows() : [];
};

window.selectAllStudents = function() {
    if (window.studentsTableSelection) {
        window.studentsTableSelection.selectAll();
    }
};

window.deselectAllStudents = function() {
    if (window.studentsTableSelection) {
        window.studentsTableSelection.deselectAll();
    }
};

window.showFloatingActions = function(count) {
    if (window.studentsFloatingActions) {
        window.studentsFloatingActions.show(count);
    }
};

window.hideFloatingActions = function() {
    if (window.studentsFloatingActions) {
        window.studentsFloatingActions.hide();
    }
};

// Reinitialize components after HTMX requests
document.addEventListener('htmx:afterRequest', function(event) {
    // Reinitialize filter selects after HTMX content updates
    initializeFilterSelects();

    // Reinitialize search field if it exists
    const searchField = document.querySelector('.mdc-search-input');
    if (searchField && typeof mdc !== 'undefined') {
        const textField = new mdc.textField.MDCTextField(searchField);
    }
});

function handleSearch(query) {
    // Add search functionality here if needed
    console.log('Searching for:', query);
}

// Handle HTMX events for modal - Let modal-htmx.js handle showing/hiding
document.addEventListener('htmx:afterRequest', function(event) {
    // Check if it's a successful student form submission
    if (event.detail.target && event.detail.target.id === 'modal-content' &&
        event.detail.xhr.status === 204) {
        // Refresh the students list after modal closes
        setTimeout(() => {
            htmx.trigger('#content-area', 'refresh');
        }, 500);
    }
});

</script>

<!-- Student Modal -->
{% include 'material_modal.html' with action_url='school:student_add' modal_title='Ajouter un élève' modal_size='xxl' submit_text='Enregistrer' hide_footer=True %}
