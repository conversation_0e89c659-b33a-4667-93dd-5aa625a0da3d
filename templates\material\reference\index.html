<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students - Management System</title>
    
    <!-- Material Design CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">


    <!-- HTML5 QR Code Scanner CSS -->
    <style>
        #qr-reader {
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }

        #qr-reader__dashboard_section_csr {
            display: none !important;
        }

        #qr-reader__camera_selection {
            margin-bottom: 16px;
        }

        #qr-reader__scan_region {
            border-radius: 8px;
            overflow: hidden;
        }

        #qr-reader__scan_region video {
            border-radius: 8px;
        }

 .fade-scale-enter {
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
    opacity: 0;
    transform: scale(0.95);
  }
  .fade-scale-enter-active {
    opacity: 1;
    transform: scale(1);
  }
  .fade-scale-leave {
    transition: opacity 0.2s ease-in, transform 0.2s ease-in;
    opacity: 1;
    transform: scale(1);
  }
  .fade-scale-leave-active {
    opacity: 0;
    transform: scale(0.95);
  }
    </style>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body x-data="{activeNav: 'home', pageTitle: 'Home'}">
    <!-- Page Preloader -->
    <div class="page-preloader" id="page-preloader">
        <div class="preloader-content">
            <div class="mdc-circular-progress mdc-circular-progress--large mdc-circular-progress--indeterminate" role="progressbar" aria-label="Loading..." aria-valuemin="0" aria-valuemax="1" id="page-preloader-spinner">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="24" cy="24" r="18" stroke-width="4"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="113.097" stroke-width="4"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="3.2"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="preloader-text">Loading...</div>
        </div>
    </div>

    <!-- Loading Overlay for UI Interactions -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="mdc-circular-progress mdc-circular-progress--medium" role="progressbar" aria-label="Processing..." aria-valuemin="0" aria-valuemax="1">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="16" cy="16" r="12.5" stroke-width="3"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="78.54" stroke-width="3"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="2.4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="loading-text" id="loading-text">Processing...</div>
        </div>
    </div>

    <!-- App Bar -->
    <div class="app-bar">
        <span class="material-icons" id="menu-btn">menu</span>
        <h1>Students</h1>
        <div class="actions">
            <span class="material-icons" id="dark-mode-toggle" title="Toggle Dark Mode">dark_mode</span>
            <span class="material-icons" id="more-menu-btn">more_vert</span>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-section">
            <a href="#" class="sidebar-item" @click="activeNav = 'home'; pageTitle='Home'" :class="{ 'active' : activeNav === 'home'}">
                <span class="material-icons">home</span>
                <span>Tableau de bord</span>
            </a>
            <a href="#" class="sidebar-item" @click="activeNav = 'students'; pageTitle='Students List'" :class="{ 'active' : activeNav === 'students'}">
                <span class="material-icons">people</span>
                <span>Elèves</span>
            </a>
            <div class="sidebar-item sidebar-item-with-submenu" :class="{ 'active' : activeNav === 'accountancy'}" data-submenu="accountancy-submenu">
                <span class="material-icons">request_quote</span>
                <span>Comptabilité</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="accountancy-submenu">
                <!-- Two-level submenu item -->
                <div class="submenu-item submenu-item-with-submenu" data-submenu="payments-submenu">
                    <span>Rapport des paiements</span>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>
                <div class="submenu submenu-level-2" id="payments-submenu">
                    <a href="#" class="submenu-item submenu-item-level-2">Paiements mensuels</a>
                    <a href="#" class="submenu-item submenu-item-level-2">Paiements en retard</a>
                    <a href="#" class="submenu-item submenu-item-level-2">Historique des paiements</a>
                    <a href="#" class="submenu-item submenu-item-level-2">Statistiques</a>
                </div>

                <div class="mdc-list-divider" role="separator"></div>
                <a href="#" class="submenu-item">Frais de scolarité</a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="levels-submenu" id="levels-menu" :class="{ 'active' : activeNav === 'levels'}">
                <span class="material-icons">leaderboard</span>
                <span>Niveaux et classes</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="levels-submenu">
                <a href="#" class="submenu-item">Niveaux</a>
                <a href="#" class="submenu-item" @click="activeNav = 'levels'; pageTitle='Levels Management'">Classes</a>
            </div>
            <div class="sidebar-item sidebar-item-with-submenu" data-submenu="arabic-exams-submenu" :class="{ 'active' : activeNav === 'arabicExams'}">
                <span class="material-icons">calculate</span>
                <span>Examens Arabes</span>
                <span class="material-icons expand-icon">expand_more</span>
            </div>
            <div class="submenu" id="arabic-exams-submenu">
                <a href="#" class="submenu-item">Notes et Moyennes</a>
                <a href="#" class="submenu-item">Résultats</a>
                <a href="#" class="submenu-item">Bulletins</a>
                <div class="mdc-list-divider" role="separator"></div>

                <!-- Two-level submenu item -->
                <div class="submenu-item submenu-item-with-submenu" data-submenu="subjects-submenu">
                    <span>Matières</span>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>
                <div class="submenu submenu-level-2" id="subjects-submenu">
                    <a href="#" class="submenu-item submenu-item-level-2">Mathématiques</a>
                    <a href="#" class="submenu-item submenu-item-level-2">Sciences</a>
                    <a href="#" class="submenu-item submenu-item-level-2">Histoire</a>
                    <a href="#" class="submenu-item submenu-item-level-2">Géographie</a>
                </div>

                <a href="#" class="submenu-item">Découpage année scolaire</a>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="main-content" x-data="studentsApp()">
        <div class="content-header">
            <span class="material-icons">arrow_back</span>
            <h2 class="page-title">
                <span x-text="pageTitle"></span>
                <span class="page-title-count" x-show="activeNav === 'students'" x-text="`(${filteredData.length})`"></span>
            </h2>
            <div class="actions" x-show="activeNav === 'students'">
                <button class="mdc-button mdc-button--raised add-btn" id="add-student-btn">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons" style="pointer-events: none;">add</span>
                    <span class="mdc-button__label">Add Student</span>
                </button>
                <span class="material-icons hidden" title="Export to Excel">description</span>
                <span class="material-icons hidden" title="Import Data">file_upload</span>
                <span class="material-icons hidden" title="Toggle View">view_module</span>
                <span class="material-icons search-icon-mobile" title="Search" id="mobile-search-btn">search</span>
                <span class="material-icons" title="Filter" id="filter-btn">filter_list</span>
            </div>
            <div class="actions" x-show="activeNav === 'levels'">
                <button class="mdc-button mdc-button--raised add-btn">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons" style="pointer-events: none;">add</span>
                    <!-- <span class="mdc-button__label">Add Level</span> -->
                </button>
                <button class="mdc-button mdc-button--outlined">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons" style="pointer-events: none;">file_download</span>
                    <span class="mdc-button__label">Export</span>
                </button>
            </div>
        </div>

        <!-- Home Dashboard Section -->
        <div x-show="activeNav === 'home'"
            x-transition:enter="fade-scale-enter"
            x-transition:enter-start=""
            x-transition:enter-end="fade-scale-enter-active"
            x-transition:leave="fade-scale-leave"
            x-transition:leave-start=""
            x-transition:leave-end="fade-scale-leave-active"
        >
            <!-- Dashboard Stats Cards -->
            <div class="dashboard-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <span class="material-icons">people</span>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">1,247</div>
                        <div class="stat-label">Total Students</div>
                        <div class="stat-change positive">
                            <span class="material-icons">trending_up</span>
                            <span>+12 this month</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon warning">
                        <span class="material-icons">payments</span>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">€45,230</div>
                        <div class="stat-label">Pending Fees</div>
                        <div class="stat-change negative">
                            <span class="material-icons">trending_down</span>
                            <span>-5% from last month</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon success">
                        <span class="material-icons">event_available</span>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">94.2%</div>
                        <div class="stat-label">Attendance Rate</div>
                        <div class="stat-change positive">
                            <span class="material-icons">trending_up</span>
                            <span>+2.1% this week</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon info">
                        <span class="material-icons">grade</span>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">15.8</div>
                        <div class="stat-label">Average Grade</div>
                        <div class="stat-change positive">
                            <span class="material-icons">trending_up</span>
                            <span>+0.3 this term</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h3 class="section-title">Quick Actions</h3>
                </div>
                <div class="quick-actions-grid">
                    <button class="quick-action-card" @click="activeNav = 'students'">
                        <div class="quick-action-icon">
                            <span class="material-icons">person_add</span>
                        </div>
                        <div class="quick-action-content">
                            <div class="quick-action-title">Add Student</div>
                            <div class="quick-action-subtitle">Register new student</div>
                        </div>
                    </button>

                    <button class="quick-action-card">
                        <div class="quick-action-icon">
                            <span class="material-icons">qr_code</span>
                        </div>
                        <div class="quick-action-content">
                            <div class="quick-action-title">Scan QR Code</div>
                            <div class="quick-action-subtitle">Quick attendance</div>
                        </div>
                    </button>

                    <button class="quick-action-card">
                        <div class="quick-action-icon">
                            <span class="material-icons">assessment</span>
                        </div>
                        <div class="quick-action-content">
                            <div class="quick-action-title">Generate Report</div>
                            <div class="quick-action-subtitle">Monthly summary</div>
                        </div>
                    </button>

                    <button class="quick-action-card">
                        <div class="quick-action-icon">
                            <span class="material-icons">notifications</span>
                        </div>
                        <div class="quick-action-content">
                            <div class="quick-action-title">Send Notice</div>
                            <div class="quick-action-subtitle">Broadcast message</div>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Recent Activity & Upcoming Events -->
            <div class="dashboard-row">
                <div class="dashboard-column">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3 class="card-title">Recent Activity</h3>
                            <button class="card-action">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="activity-list">
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <span class="material-icons">person_add</span>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">New student registered</div>
                                        <div class="activity-subtitle">Sarah Johnson - Grade 10</div>
                                        <div class="activity-time">2 hours ago</div>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <span class="material-icons">payment</span>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">Fee payment received</div>
                                        <div class="activity-subtitle">€450 from Alex Martin</div>
                                        <div class="activity-time">4 hours ago</div>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <span class="material-icons">grade</span>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">Grades updated</div>
                                        <div class="activity-subtitle">Mathematics - Grade 11</div>
                                        <div class="activity-time">6 hours ago</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-column">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3 class="card-title">Upcoming Events</h3>
                            <button class="card-action">
                                <span class="material-icons">add</span>
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="event-list">
                                <div class="event-item">
                                    <div class="event-date">
                                        <div class="event-day">15</div>
                                        <div class="event-month">Dec</div>
                                    </div>
                                    <div class="event-content">
                                        <div class="event-title">Parent-Teacher Meeting</div>
                                        <div class="event-time">
                                            <span class="material-icons">schedule</span>
                                            <span>2:00 PM - 5:00 PM</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="event-item">
                                    <div class="event-date">
                                        <div class="event-day">18</div>
                                        <div class="event-month">Dec</div>
                                    </div>
                                    <div class="event-content">
                                        <div class="event-title">Winter Break Begins</div>
                                        <div class="event-time">
                                            <span class="material-icons">event</span>
                                            <span>All Day</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="event-item">
                                    <div class="event-date">
                                        <div class="event-day">22</div>
                                        <div class="event-month">Dec</div>
                                    </div>
                                    <div class="event-content">
                                        <div class="event-title">Grade Reports Due</div>
                                        <div class="event-time">
                                            <span class="material-icons">assignment</span>
                                            <span>End of Day</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Levels Management Section -->
        <div x-show="activeNav === 'levels'"
            x-data="{ activeTab: 'french' }"
            x-transition:enter="fade-scale-enter"
            x-transition:enter-start=""
            x-transition:enter-end="fade-scale-enter-active"
            x-transition:leave="fade-scale-leave"
            x-transition:leave-start=""
            x-transition:leave-end="fade-scale-leave-active"
        >
            <!-- Levels Tabs - Material Design M2 -->
            <div class="mdc-tab-bar" role="tablist">
                <div class="mdc-tab-scroller">
                    <div class="mdc-tab-scroller__scroll-area">
                        <div class="mdc-tab-scroller__scroll-content">
                            <button class="mdc-tab" :class="{ 'mdc-tab--active': activeTab === 'french' }"
                                    role="tab" :aria-selected="activeTab === 'french'"
                                    @click="activeTab = 'french'">
                                <span class="mdc-tab__content">
                                    <span class="mdc-tab__icon material-icons">translate</span>
                                    <span class="mdc-tab__text-label">French Levels</span>
                                </span>
                                <span class="mdc-tab-indicator" :class="{ 'mdc-tab-indicator--active': activeTab === 'french' }">
                                    <span class="mdc-tab-indicator__content mdc-tab-indicator__content--underline"></span>
                                </span>
                                <span class="mdc-tab__ripple"></span>
                            </button>
                            <button class="mdc-tab" :class="{ 'mdc-tab--active': activeTab === 'arabic' }"
                                    role="tab" :aria-selected="activeTab === 'arabic'"
                                    @click="activeTab = 'arabic'">
                                <span class="mdc-tab__content">
                                    <span class="mdc-tab__icon material-icons">language</span>
                                    <span class="mdc-tab__text-label">Arabic Levels</span>
                                </span>
                                <span class="mdc-tab-indicator" :class="{ 'mdc-tab-indicator--active': activeTab === 'arabic' }">
                                    <span class="mdc-tab-indicator__content mdc-tab-indicator__content--underline"></span>
                                </span>
                                <span class="mdc-tab__ripple"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- French Levels Table -->
            <div class="levels-content" x-show="activeTab === 'french'" id="french-levels">

                <!-- French Levels Data Table -->
                <div class="mdc-data-table">
                    <div class="mdc-data-table__table-container">
                        <table class="mdc-data-table__table" aria-label="French levels list">
                            <thead>
                                <tr class="mdc-data-table__header-row">
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" role="columnheader" scope="col">
                                        <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                                            <input type="checkbox" class="mdc-checkbox__native-control" id="select-all-french-levels" aria-label="Toggle all rows selected">
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                            <div class="mdc-checkbox__ripple"></div>
                                        </div>
                                    </th>
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable" role="columnheader" scope="col">
                                        Level Name
                                        <span class="material-icons sort-icon">arrow_upward</span>
                                    </th>
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column" role="columnheader" scope="col">
                                        Students Count
                                        <span class="material-icons sort-icon">unfold_more</span>
                                    </th>
                                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="mdc-data-table__content">
                                <tr class="mdc-data-table__row">
                                    <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                        <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                            <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                            <div class="mdc-checkbox__ripple"></div>
                                        </div>
                                    </td>
                                    <th class="mdc-data-table__cell" scope="row">6ème</th>
                                    <td class="mdc-data-table__cell numeric-cell">142</td>
                                    <td class="mdc-data-table__cell">
                                        <div class="table-actions">
                                            <button class="action-btn edit-btn" title="Edit Level">
                                                <span class="material-icons">edit</span>
                                            </button>
                                            <button class="action-btn delete-btn" title="Delete Level">
                                                <span class="material-icons">delete</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="mdc-data-table__row">
                                    <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                        <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                            <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                            <div class="mdc-checkbox__ripple"></div>
                                        </div>
                                    </td>
                                    <th class="mdc-data-table__cell" scope="row">5ème</th>
                                    <td class="mdc-data-table__cell numeric-cell">138</td>
                                    <td class="mdc-data-table__cell">
                                        <div class="table-actions">
                                            <button class="action-btn edit-btn" title="Edit Level">
                                                <span class="material-icons">edit</span>
                                            </button>
                                            <button class="action-btn delete-btn" title="Delete Level">
                                                <span class="material-icons">delete</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="mdc-data-table__row">
                                    <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                        <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                            <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                            <div class="mdc-checkbox__ripple"></div>
                                        </div>
                                    </td>
                                    <th class="mdc-data-table__cell" scope="row">4ème</th>
                                    <td class="mdc-data-table__cell numeric-cell">156</td>
                                    <td class="mdc-data-table__cell">
                                        <div class="table-actions">
                                            <button class="action-btn edit-btn" title="Edit Level">
                                                <span class="material-icons">edit</span>
                                            </button>
                                            <button class="action-btn delete-btn" title="Delete Level">
                                                <span class="material-icons">delete</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="mdc-data-table__row">
                                    <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                        <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                            <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                            <div class="mdc-checkbox__ripple"></div>
                                        </div>
                                    </td>
                                    <th class="mdc-data-table__cell" scope="row">3ème</th>
                                    <td class="mdc-data-table__cell numeric-cell">134</td>
                                    <td class="mdc-data-table__cell">
                                        <div class="table-actions">
                                            <button class="action-btn edit-btn" title="Edit Level">
                                                <span class="material-icons">edit</span>
                                            </button>
                                            <button class="action-btn delete-btn" title="Delete Level">
                                                <span class="material-icons">delete</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Arabic Levels Table -->
            <div class="levels-content" x-show="activeTab === 'arabic'" id="arabic-levels">

                <!-- Arabic Levels Data Table -->
                <div class="mdc-data-table">
                    <div class="mdc-data-table__table-container">
                        <table class="mdc-data-table__table" aria-label="Arabic levels list">
                            <thead>
                                <tr class="mdc-data-table__header-row">
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" role="columnheader" scope="col">
                                        <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                                            <input type="checkbox" class="mdc-checkbox__native-control" id="select-all-arabic-levels" aria-label="Toggle all rows selected">
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                            <div class="mdc-checkbox__ripple"></div>
                                        </div>
                                    </th>
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable" role="columnheader" scope="col">
                                        Level Name
                                        <span class="material-icons sort-icon">arrow_upward</span>
                                    </th>
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column" role="columnheader" scope="col">
                                        Students Count
                                        <span class="material-icons sort-icon">unfold_more</span>
                                    </th>
                                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="mdc-data-table__content">
                                <tr class="mdc-data-table__row">
                                    <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                        <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                            <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                            <div class="mdc-checkbox__ripple"></div>
                                        </div>
                                    </td>
                                    <th class="mdc-data-table__cell" scope="row">الصف السادس</th>
                                    <td class="mdc-data-table__cell numeric-cell">142</td>
                                    <td class="mdc-data-table__cell">
                                        <div class="table-actions">
                                            <button class="action-btn edit-btn" title="Edit Level">
                                                <span class="material-icons">edit</span>
                                            </button>
                                            <button class="action-btn delete-btn" title="Delete Level">
                                                <span class="material-icons">delete</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="mdc-data-table__row">
                                    <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                        <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                            <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                            <div class="mdc-checkbox__ripple"></div>
                                        </div>
                                    </td>
                                    <th class="mdc-data-table__cell" scope="row">الصف الخامس</th>
                                    <td class="mdc-data-table__cell numeric-cell">138</td>
                                    <td class="mdc-data-table__cell">
                                        <div class="table-actions">
                                            <button class="action-btn edit-btn" title="Edit Level">
                                                <span class="material-icons">edit</span>
                                            </button>
                                            <button class="action-btn delete-btn" title="Delete Level">
                                                <span class="material-icons">delete</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="mdc-data-table__row">
                                    <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                        <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                            <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                            <div class="mdc-checkbox__ripple"></div>
                                        </div>
                                    </td>
                                    <th class="mdc-data-table__cell" scope="row">الصف الرابع</th>
                                    <td class="mdc-data-table__cell numeric-cell">156</td>
                                    <td class="mdc-data-table__cell">
                                        <div class="table-actions">
                                            <button class="action-btn edit-btn" title="Edit Level">
                                                <span class="material-icons">edit</span>
                                            </button>
                                            <button class="action-btn delete-btn" title="Delete Level">
                                                <span class="material-icons">delete</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="mdc-data-table__row">
                                    <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                        <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                            <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                            <div class="mdc-checkbox__ripple"></div>
                                        </div>
                                    </td>
                                    <th class="mdc-data-table__cell" scope="row">الصف الثالث</th>
                                    <td class="mdc-data-table__cell numeric-cell">134</td>
                                    <td class="mdc-data-table__cell">
                                        <div class="table-actions">
                                            <button class="action-btn edit-btn" title="Edit Level">
                                                <span class="material-icons">edit</span>
                                            </button>
                                            <button class="action-btn delete-btn" title="Delete Level">
                                                <span class="material-icons">delete</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div x-show="activeNav === 'students'"
            x-transition:enter="fade-scale-enter"
            x-transition:enter-start=""
            x-transition:enter-end="fade-scale-enter-active"
            x-transition:leave="fade-scale-leave"
            x-transition:leave-start=""
            x-transition:leave-end="fade-scale-leave-active"
        >
            <!-- Quick Filter Chips -->
            <div class="quick-filter-chips">
                <button class="quick-filter-chip active" data-filter="all" @click="setQuickFilter('all')">
                    <span class="material-icons">group</span>
                    <span>All</span>
                </button>
                <button class="quick-filter-chip" data-filter="paid" @click="setQuickFilter('paid')">
                    <span class="material-icons">paid</span>
                    <span>Paid</span>
                </button>
                <button class="quick-filter-chip" data-filter="no-payments" @click="setQuickFilter('no-payments')">
                    <span class="material-icons">money_off</span>
                    <span>No Pay</span>
                </button>
                <button class="quick-filter-chip" data-filter="all-paid" @click="setQuickFilter('all-paid')">
                    <span class="material-icons">check_circle</span>
                    <span>Full Paid</span>
                </button>
                <button class="quick-filter-chip" data-filter="enrolled-today" @click="setQuickFilter('enrolled-today')">
                    <span class="material-icons">today</span>
                    <span>Today</span>
                </button>
            </div>
    
            <!-- Filter Chips Container -->
            <div class="filter-chips-container" id="filter-chips-container">
                <!-- Filter chips will be dynamically added here -->
            </div>
    
            <!-- Data Table (Desktop) -->
            <div class="data-table-container">
                <!-- Targeted Loading Overlay for Table -->
                <div class="target-loading-overlay" id="table-loading-overlay">
                    <div class="target-loading-content">
                        <div class="simple-spinner">
                            <svg class="spinner-svg" viewBox="0 0 50 50">
                                <circle class="spinner-circle" cx="25" cy="25" r="20" fill="none" stroke="#1976d2" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                                    <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                                    <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                                </circle>
                            </svg>
                        </div>
                        <div class="target-loading-text" id="table-loading-text">Loading...</div>
                    </div>
                </div>
    
                <!-- Table Controls -->
                <div class="table-controls">
                    <div class="search-container">
                        <span class="search-label">Search:</span>
                        <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon mdc-small-input mdc-search-input">
                            <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">search</span>
                            <input type="text" class="mdc-text-field__input" id="table-search" placeholder="Search students..." x-model="searchQuery">
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__notch"></div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                    </div>
                    <div class="per-page-container">
                        <span class="per-page-label">Rows per page:</span>
                        <div class="mdc-select mdc-select--outlined mdc-small-input" id="per-page-select">
                            <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false">
                                <span class="mdc-select__selected-text-container">
                                    <span class="mdc-select__selected-text" x-text="perPage"></span>
                                </span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                                <div class="mdc-notched-outline">
                                    <div class="mdc-notched-outline__leading"></div>
                                    <div class="mdc-notched-outline__trailing"></div>
                                </div>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-deprecated-list" role="listbox">
                                    <li class="mdc-deprecated-list-item" data-value="5" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">5</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="10" role="option" aria-selected="true">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">10</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="25" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">25</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="50" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">50</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
    
                <!-- MDC Data Table -->
                <div class="mdc-data-table" x-show="filteredData.length > 0">
                    <div class="mdc-data-table__table-container">
                        <table class="mdc-data-table__table" aria-label="Students list">
                            <thead>
                                <tr class="mdc-data-table__header-row">
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" role="columnheader" scope="col">
                                        <div class="mdc-checkbox mdc-data-table__header-row-checkbox" :class="{ 'mdc-checkbox--selected': allVisibleSelected }">
                                            <input type="checkbox" class="mdc-checkbox__native-control" id="select-all-checkbox" aria-label="Toggle all rows selected"
                                                   :checked="allVisibleSelected"
                                                   :indeterminate="someVisibleSelected && !allVisibleSelected"
                                                   @change="selectAllStudents()">
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                            <div class="mdc-checkbox__ripple"></div>
                                        </div>
                                    </th>
                                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Photo</th>
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                        :class="getSortClass('name')"
                                        role="columnheader" scope="col" data-column="name"
                                        @click="sortData('name')">
                                        Full Name
                                        <span class="material-icons sort-icon" x-text="getSortIcon('name')"></span>
                                    </th>
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                        :class="getSortClass('id')"
                                        role="columnheader" scope="col" data-column="id"
                                        @click="sortData('id')">
                                        Student ID
                                        <span class="material-icons sort-icon" x-text="getSortIcon('id')"></span>
                                    </th>
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                        :class="getSortClass('gender')"
                                        role="columnheader" scope="col" data-column="gender"
                                        @click="sortData('gender')">
                                        Gender
                                        <span class="material-icons sort-icon" x-text="getSortIcon('gender')"></span>
                                    </th>
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                        :class="getSortClass('levelFr')"
                                        role="columnheader" scope="col" data-column="levelFr"
                                        @click="sortData('levelFr')">
                                        Grade (FR)
                                        <span class="material-icons sort-icon" x-text="getSortIcon('levelFr')"></span>
                                    </th>
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                        :class="getSortClass('levelAr')"
                                        role="columnheader" scope="col" data-column="levelAr"
                                        @click="sortData('levelAr')">
                                        Grade (AR)
                                        <span class="material-icons sort-icon" x-text="getSortIcon('levelAr')"></span>
                                    </th>
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                        :class="getSortClass('status')"
                                        role="columnheader" scope="col" data-column="status"
                                        @click="sortData('status')">
                                        Status
                                        <span class="material-icons sort-icon" x-text="getSortIcon('status')"></span>
                                    </th>
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column"
                                        :class="getSortClass('fees')"
                                        role="columnheader" scope="col" data-column="fees"
                                        @click="sortData('fees')">
                                        Fees
                                        <span class="material-icons sort-icon" x-text="getSortIcon('fees')"></span>
                                    </th>
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column"
                                        :class="getSortClass('amountPaid')"
                                        role="columnheader" scope="col" data-column="amountPaid"
                                        @click="sortData('amountPaid')">
                                        Amount Paid
                                        <span class="material-icons sort-icon" x-text="getSortIcon('amountPaid')"></span>
                                    </th>
                                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column"
                                        :class="getSortClass('remaining')"
                                        role="columnheader" scope="col" data-column="remaining"
                                        @click="sortData('remaining')">
                                        Remaining
                                        <span class="material-icons sort-icon" x-text="getSortIcon('remaining')"></span>
                                    </th>
                                    <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="mdc-data-table__content" id="table-body">
                                <!-- Alpine.js Table Rows Template -->
                                <template x-if="paginatedData.length === 0">
                                    <tr class="mdc-data-table__row">
                                        <td class="mdc-data-table__cell" colspan="12" style="text-align: center; padding: 48px;">
                                            <div style="color: #757575;">
                                                <span class="material-icons" style="font-size: 48px; margin-bottom: 16px; display: block;">search_off</span>
                                                No students found
                                            </div>
                                        </td>
                                    </tr>
                                </template>
    
                                <template x-for="student in paginatedData" :key="student.id">
                                    <tr class="mdc-data-table__row table-row"
                                        :class="{ 'mdc-data-table__row--selected': isStudentSelected(student.id) }"
                                        :data-student-id="student.id"
                                        @click="handleTableRowClick($event, student)">
                                        <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                            <div class="mdc-checkbox mdc-data-table__row-checkbox" :class="{ 'mdc-checkbox--selected': isStudentSelected(student.id) }">
                                                <input type="checkbox" class="mdc-checkbox__native-control row-checkbox" aria-label="Select row"
                                                       :checked="isStudentSelected(student.id)"
                                                       @change="toggleStudentSelection(student.id)"
                                                       @click.stop>
                                                <div class="mdc-checkbox__background">
                                                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                        <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                    </svg>
                                                    <div class="mdc-checkbox__mixedmark"></div>
                                                </div>
                                                <div class="mdc-checkbox__ripple"></div>
                                            </div>
                                        </td>
                                        <td class="mdc-data-table__cell">
                                            <div class="student-photo" :style="`background-image: url('${student.photo}')`"></div>
                                        </td>
                                        <th class="mdc-data-table__cell" scope="row" x-text="student.name"></th>
                                        <td class="mdc-data-table__cell" x-text="student.id"></td>
                                        <td class="mdc-data-table__cell" x-text="student.gender"></td>
                                        <td class="mdc-data-table__cell" x-text="student.levelFr"></td>
                                        <td class="mdc-data-table__cell" x-text="student.levelAr"></td>
                                        <td class="mdc-data-table__cell" x-text="student.status"></td>
                                        <td class="mdc-data-table__cell numeric-cell" x-text="student.fees.toLocaleString()"></td>
                                        <td class="mdc-data-table__cell numeric-cell" x-text="student.amountPaid.toLocaleString()"></td>
                                        <td class="mdc-data-table__cell numeric-cell" x-text="student.remaining.toLocaleString()"></td>
                                        <td class="mdc-data-table__cell">
                                            <div class="table-actions">
                                                <button class="action-btn edit-btn" title="Edit Student" @click.stop="console.log('Edit student:', student.name)">
                                                    <span class="material-icons">edit</span>
                                                </button>
                                                <button class="action-btn delete-btn" title="Delete Student" @click.stop="deleteStudent(student)">
                                                    <span class="material-icons">delete</span>
                                                </button>
                                                <button class="action-btn more-btn" title="More Options" @click.stop="handleMoreAction($event, student)">
                                                    <span class="material-icons">more_vert</span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    <!-- Pagination -->
                    <div class="table-pagination">
                        <div class="pagination-info" id="pagination-info">
                            <span x-text="`Showing ${((currentPage - 1) * perPage) + 1}-${Math.min(currentPage * perPage, filteredData.length)} of ${filteredData.length} students`"></span>
                        </div>
                        <div class="pagination-controls">
                            <button class="mdc-icon-button"
                                    :disabled="currentPage <= 1"
                                    @click="prevPage()">
                                <span class="material-icons">chevron_left</span>
                            </button>
                            <div class="pagination-pages" id="pagination-pages">
                                <template x-for="page in visiblePages" :key="page">
                                    <button class="pagination-page"
                                            :class="{ 'active': page === currentPage, 'ellipsis': page === '...' }"
                                            :disabled="page === '...'"
                                            @click="page !== '...' && goToPage(page)"
                                            x-text="page"></button>
                                </template>
                            </div>
                            <button class="mdc-icon-button"
                                    :disabled="currentPage >= totalPages"
                                    @click="nextPage()">
                                <span class="material-icons">chevron_right</span>
                            </button>
                        </div>
                    </div>
                </div>
    
            </div>
    
            <!-- No Data Found Component -->
            <div class="no-data-found" x-show="filteredData.length === 0">
                <div class="no-data-content">
                    <span class="material-icons no-data-icon">search_off</span>
                    <div class="no-data-title">No students found</div>
                    <div class="no-data-message">Try adjusting your search or filter criteria</div>
                    <button class="mdc-button mdc-button--outlined no-data-action" @click="clearAllFilters()">
                        <span class="mdc-button__ripple"></span>
                        <span class="mdc-button__label">Clear Filters</span>
                    </button>
                </div>
            </div>
    
            <!-- Students List (Mobile) -->
            <div class="students-list">
                <!-- Targeted Loading Overlay for Mobile List -->
                <div class="target-loading-overlay" id="list-loading-overlay">
                    <div class="target-loading-content">
                        <div class="simple-spinner">
                            <svg class="spinner-svg" viewBox="0 0 50 50">
                                <circle class="spinner-circle" cx="25" cy="25" r="20" fill="none" stroke="#1976d2" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                                    <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                                    <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                                </circle>
                            </svg>
                        </div>
                        <div class="target-loading-text" id="list-loading-text">Loading...</div>
                    </div>
                </div>
    
                <!-- Alpine.js Mobile Students Template -->
                <template x-for="(student, index) in mobileDisplayData" :key="student.id">
                    <div class="student-item"
                         :data-student-id="student.id"
                         @click="showBottomSheet({
                             id: student.id,
                             name: student.name,
                             level: student.level,
                             details: `Born: ${student.birth} • Student ID: ${student.id}`,
                             photo: `url('${student.photo}')`
                         })">
                        <div class="student-photo" :style="`background-image: url('${student.photo}')`"></div>
                        <div class="student-info">
                            <div class="student-header">
                                <div class="student-name" x-text="student.name"></div>
                                <div class="student-id" x-text="student.id"></div>
                            </div>
                            <div class="student-name-ar" x-text="student.nameAr"></div>
                            <div class="student-birth-date" x-text="formatBirthDate(student.birth) + ' • ' + (student.birthPlace.length > 16 ? student.birthPlace.substring(0, 16) + '...' : student.birthPlace)"></div>
                            <div class="student-grades">
                                <span class="grade-fr" x-text="student.levelFr"></span>
                                <span class="grade-separator">•</span>
                                <span class="grade-ar" x-text="student.levelAr"></span>
                            </div>
                        </div>
                        <div class="student-actions">
                            <button class="edit-btn" title="Edit Student" @click.stop="console.log('Edit student:', student.name)">
                                <span class="material-icons">edit</span>
                            </button>
                            <button class="delete-btn" title="Delete Student" @click.stop="deleteStudent(student)">
                                <span class="material-icons">delete</span>
                            </button>
                        </div>
                    </div>
                </template>
    
                <!-- Loading More Spinner Before New Content -->
                <div class="loading-more-spinner" x-show="isLoadingMore && hasMoreMobileData">
                    <div class="spinner-container">
                        <div class="mdc-circular-progress mdc-circular-progress--small mdc-circular-progress--indeterminate" role="progressbar" aria-label="Loading more students...">
                            <div class="mdc-circular-progress__determinate-container">
                                <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <circle class="mdc-circular-progress__determinate-track" cx="12" cy="12" r="8.5" stroke-width="2.5"/>
                                    <circle class="mdc-circular-progress__determinate-circle" cx="12" cy="12" r="8.5" stroke-dasharray="53.407" stroke-dashoffset="53.407" stroke-width="2.5"/>
                                </svg>
                            </div>
                            <div class="mdc-circular-progress__indeterminate-container">
                                <div class="mdc-circular-progress__spinner-layer">
                                    <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                                        <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="12" r="8.5" stroke-dasharray="53.407" stroke-dashoffset="26.704" stroke-width="2.5"/>
                                        </svg>
                                    </div>
                                    <div class="mdc-circular-progress__gap-patch">
                                        <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="12" r="8.5" stroke-dasharray="53.407" stroke-dashoffset="26.704" stroke-width="2"/>
                                        </svg>
                                    </div>
                                    <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                                        <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="12" r="8.5" stroke-dasharray="53.407" stroke-dashoffset="26.704" stroke-width="2.5"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="loading-more-text">Loading more students...</div>
                </div>
    
                <!-- End of Results Message -->
                <div class="end-of-results" x-show="!hasMoreMobileData && mobileDisplayData.length > 0">
                    <div class="end-of-results-content">
                        <span class="material-icons">check_circle</span>
                        <div class="end-of-results-text">You've reached the end of the list</div>
                        <div class="end-of-results-count" x-text="`Showing all ${filteredData.length} students`"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Button (temporary) -->
        <!-- <button @click="selectedStudentIds.add('test')" style="position: fixed; top: 200px; right: 20px; z-index: 9999; background: red; color: white; padding: 10px;">
            Test Bulk Bar
        </button> -->

        <!-- Bulk Action Bar -->
        <div class="bulk-action-bar" :class="{ 'visible': selectedStudentIds.size > 0 }" x-transition>
            <div class="bulk-selected-count" x-text="selectedStudentIds.size === 1 ? '1 selected' : `${selectedStudentIds.size} selected`"></div>
            <div class="bulk-actions">
                <button class="bulk-action-btn archive" @click="archiveSelectedStudents()">
                    <span class="material-icons">archive</span>
                    <span>Archive</span>
                </button>
                <button class="bulk-action-btn delete" @click="deleteSelectedStudents()">
                    <span class="material-icons">delete</span>
                    <span>Delete</span>
                </button>
            </div>
        </div>


    </div>

    <!-- Bottom App Bar (Mobile) -->
    <div class="bottom-app-bar" id="bottom-app-bar">
        <div class="bottom-nav-item" id="nav-home">
            <span class="material-icons">home</span>
            <span class="bottom-nav-label">Home</span>
        </div>
        <div class="bottom-nav-item active" id="nav-students">
            <span class="material-icons">people</span>
            <span class="bottom-nav-label">Students</span>
        </div>
        <div class="bottom-nav-item" id="nav-stats">
            <span class="material-icons">analytics</span>
            <span class="bottom-nav-label">Stats</span>
        </div>
        <div class="bottom-nav-item" id="nav-attendance">
            <span class="material-icons">event_available</span>
            <span class="bottom-nav-label">Attendance</span>
        </div>
    </div>

    <!-- QR Code Floating Action Button -->
    <button class="mdc-fab qr-fab" id="qr-fab">
        <div class="mdc-fab__ripple"></div>
        <span class="material-icons mdc-fab__icon">qr_code</span>
    </button>

    <!-- Add Student Floating Action Button -->
    <button class="mdc-fab mdc-fab--extended" id="add-student-fab">
        <div class="mdc-fab__ripple"></div>
        <span class="material-icons mdc-fab__icon">add</span>
        <span class="mdc-fab__label">Add Student</span>
    </button>



    <!-- Student Detail Page -->
    <div class="student-detail-overlay" id="student-detail-overlay">
        <div class="student-detail-page" id="student-detail-page">
            <!-- Header -->
            <div class="student-detail-header">
                <button class="student-detail-back" id="student-detail-back">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="student-detail-title">Student Details</div>
                <button class="student-detail-more" id="student-detail-more">
                    <span class="material-icons">more_vert</span>
                </button>
            </div>

            <!-- Content -->
            <div class="student-detail-content" id="student-detail-content">
                <!-- Student Profile Section -->
                <div class="student-profile-section">
                    <div class="student-profile-photo" id="detail-photo"></div>
                    <div class="student-profile-info">
                        <div class="student-profile-name" id="detail-name"></div>
                        <div class="student-profile-name-ar" id="detail-name-ar"></div>
                        <div class="student-profile-id" id="detail-id"></div>
                    </div>
                </div>

                <!-- General Information -->
                <div class="detail-section">
                    <div class="detail-section-header">
                        <span class="material-icons">person</span>
                        <span class="detail-section-title">General Information</span>
                    </div>
                    <div class="detail-section-content">
                        <div class="detail-item">
                            <div class="detail-label">Birth Date</div>
                            <div class="detail-value" id="detail-birth"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Birth Place</div>
                            <div class="detail-value" id="detail-birth-place"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Gender</div>
                            <div class="detail-value" id="detail-gender"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Status</div>
                            <div class="detail-value" id="detail-status"></div>
                        </div>
                    </div>
                </div>

                <!-- Academic Information -->
                <div class="detail-section">
                    <div class="detail-section-header">
                        <span class="material-icons">school</span>
                        <span class="detail-section-title">Academic Information</span>
                    </div>
                    <div class="detail-section-content">
                        <div class="detail-item">
                            <div class="detail-label">Grade (French)</div>
                            <div class="detail-value" id="detail-grade-fr"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Grade (Arabic)</div>
                            <div class="detail-value" id="detail-grade-ar"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Academic Year</div>
                            <div class="detail-value">2024-2025</div>
                        </div>
                    </div>
                </div>

                <!-- Financial Information -->
                <div class="detail-section">
                    <div class="detail-section-header">
                        <span class="material-icons">payments</span>
                        <span class="detail-section-title">Financial Information</span>
                    </div>
                    <div class="detail-section-content">
                        <div class="detail-item">
                            <div class="detail-label">Total Fees</div>
                            <div class="detail-value" id="detail-fees"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Amount Paid</div>
                            <div class="detail-value" id="detail-paid"></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Remaining</div>
                            <div class="detail-value" id="detail-remaining"></div>
                        </div>
                    </div>
                </div>

                <!-- Results Section -->
                <div class="detail-section">
                    <div class="detail-section-header">
                        <span class="material-icons">grade</span>
                        <span class="detail-section-title">Results</span>
                    </div>
                    <div class="detail-section-content">
                        <div class="results-tabs">
                            <button class="results-tab active" data-tab="french">French</button>
                            <button class="results-tab" data-tab="arabic">Arabic</button>
                        </div>
                        <div class="results-content">
                            <div class="results-tab-content active" id="results-french">
                                <div class="result-item">
                                    <div class="result-subject">Mathematics</div>
                                    <div class="result-grade">16/20</div>
                                </div>
                                <div class="result-item">
                                    <div class="result-subject">Physics</div>
                                    <div class="result-grade">14/20</div>
                                </div>
                                <div class="result-item">
                                    <div class="result-subject">French</div>
                                    <div class="result-grade">15/20</div>
                                </div>
                                <div class="result-item">
                                    <div class="result-subject">History</div>
                                    <div class="result-grade">13/20</div>
                                </div>
                            </div>
                            <div class="results-tab-content" id="results-arabic">
                                <div class="result-item">
                                    <div class="result-subject">الرياضيات</div>
                                    <div class="result-grade">16/20</div>
                                </div>
                                <div class="result-item">
                                    <div class="result-subject">الفيزياء</div>
                                    <div class="result-grade">14/20</div>
                                </div>
                                <div class="result-item">
                                    <div class="result-subject">اللغة العربية</div>
                                    <div class="result-grade">17/20</div>
                                </div>
                                <div class="result-item">
                                    <div class="result-subject">التاريخ</div>
                                    <div class="result-grade">13/20</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Attendance Section -->
                <div class="detail-section">
                    <div class="detail-section-header">
                        <span class="material-icons">event_available</span>
                        <span class="detail-section-title">Attendance</span>
                    </div>
                    <div class="detail-section-content">
                        <div class="attendance-summary">
                            <div class="attendance-stat">
                                <div class="attendance-number">92%</div>
                                <div class="attendance-label">Attendance Rate</div>
                            </div>
                            <div class="attendance-stat">
                                <div class="attendance-number">138</div>
                                <div class="attendance-label">Present Days</div>
                            </div>
                            <div class="attendance-stat">
                                <div class="attendance-number">12</div>
                                <div class="attendance-label">Absent Days</div>
                            </div>
                        </div>
                        <div class="attendance-calendar">
                            <div class="calendar-header">Recent Attendance</div>
                            <div class="calendar-days">
                                <div class="calendar-day present" title="Present">M</div>
                                <div class="calendar-day present" title="Present">T</div>
                                <div class="calendar-day absent" title="Absent">W</div>
                                <div class="calendar-day present" title="Present">T</div>
                                <div class="calendar-day present" title="Present">F</div>
                                <div class="calendar-day present" title="Present">M</div>
                                <div class="calendar-day present" title="Present">T</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Sheet -->
    <div class="bottom-sheet-overlay" id="bottom-sheet-overlay">
        <div class="bottom-sheet" id="bottom-sheet">
            <div class="bottom-sheet-handle"></div>
            <div class="bottom-sheet-header">
                <div class="bottom-sheet-title">Student Options</div>
                <button class="bottom-sheet-close" id="bottom-sheet-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="bottom-sheet-content">
                <div class="student-preview" id="student-preview">
                    <div class="student-photo" id="preview-photo"></div>
                    <div class="student-info">
                        <div class="student-name" id="preview-name"></div>
                        <div class="student-details" id="preview-details"></div>
                    </div>
                </div>

                <div class="bottom-sheet-actions">
                    <div class="bottom-sheet-action" id="action-details">
                        <span class="material-icons">person</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">View Details</div>
                            <div class="bottom-sheet-action-subtitle">See complete student information</div>
                        </div>
                    </div>

                    <div class="bottom-sheet-action" id="action-edit">
                        <span class="material-icons">edit</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Edit Student</div>
                            <div class="bottom-sheet-action-subtitle">Update student information</div>
                        </div>
                    </div>

                    <div class="bottom-sheet-action" id="action-payment">
                        <span class="material-icons">payment</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Add Payment Record</div>
                            <div class="bottom-sheet-action-subtitle">Record a new payment</div>
                        </div>
                    </div>

                    <div class="bottom-sheet-action delete" id="action-delete">
                        <span class="material-icons">delete</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Delete Student</div>
                            <div class="bottom-sheet-action-subtitle">Remove student from system</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Offcanvas Component -->
    <div class="offcanvas-overlay" id="offcanvas-overlay">
        <div class="offcanvas" id="offcanvas">
            <div class="offcanvas-header">
                <div class="offcanvas-title">User Options</div>
                <button class="offcanvas-close" id="offcanvas-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="offcanvas-content">
                <!-- User Profile Section -->
                <div class="offcanvas-profile">
                    <div class="profile-avatar">
                        <span class="material-icons">account_circle</span>
                    </div>
                    <div class="profile-info">
                        <div class="profile-name">John Doe</div>
                        <div class="profile-email"><EMAIL></div>
                    </div>
                </div>

                <!-- Menu Options -->
                <div class="offcanvas-menu">
                    <div class="offcanvas-menu-item" id="menu-profile">
                        <span class="material-icons">person</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Edit Profile</div>
                            <div class="menu-item-subtitle">Update your personal information</div>
                        </div>
                        <span class="material-icons menu-item-arrow">chevron_right</span>
                    </div>

                    <div class="offcanvas-menu-item" id="menu-settings">
                        <span class="material-icons">settings</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Settings</div>
                            <div class="menu-item-subtitle">App preferences and configuration</div>
                        </div>
                        <span class="material-icons menu-item-arrow">chevron_right</span>
                    </div>

                    <div class="offcanvas-menu-item" id="menu-notifications">
                        <span class="material-icons">notifications</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Notifications</div>
                            <div class="menu-item-subtitle">Manage notification preferences</div>
                        </div>
                        <span class="material-icons menu-item-arrow">chevron_right</span>
                    </div>

                    <div class="offcanvas-menu-item" id="menu-help">
                        <span class="material-icons">help</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Help & Support</div>
                            <div class="menu-item-subtitle">Get help and contact support</div>
                        </div>
                        <span class="material-icons menu-item-arrow">chevron_right</span>
                    </div>

                    <div class="offcanvas-divider"></div>

                    <div class="offcanvas-menu-item logout" id="menu-logout">
                        <span class="material-icons">logout</span>
                        <div class="menu-item-content">
                            <div class="menu-item-title">Logout</div>
                            <div class="menu-item-subtitle">Sign out of your account</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Form Component -->
    <div class="modal-overlay" id="modal-overlay">
        <div class="modal" id="modal">
            <div class="modal-header">
                <h2 class="modal-title" id="modal-title">Add Student</h2>
                <button class="modal-close" id="modal-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content">
                <form class="modal-form" id="modal-form">
                    <!-- Form fields will be dynamically populated -->
                </form>
            </div>
            <div class="modal-actions">
                <button type="button" class="mdc-button mdc-button--outlined" id="modal-cancel">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Cancel</span>
                </button>
                <button type="submit" class="mdc-button mdc-button--raised" id="modal-submit" form="modal-form">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Save</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Confirmation Dialog Component -->
    <div class="confirm-dialog-overlay" id="confirm-dialog-overlay">
        <div class="confirm-dialog" id="confirm-dialog">
            <div class="confirm-dialog-icon" id="confirm-dialog-icon">
                <span class="material-icons">warning</span>
            </div>
            <div class="confirm-dialog-content">
                <h3 class="confirm-dialog-title" id="confirm-dialog-title">Confirm Action</h3>
                <p class="confirm-dialog-message" id="confirm-dialog-message">Are you sure you want to proceed?</p>
            </div>
            <div class="confirm-dialog-actions">
                <button type="button" class="mdc-button mdc-button--outlined" id="confirm-dialog-cancel">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Cancel</span>
                </button>
                <button type="button" class="mdc-button mdc-button--raised confirm-dialog-confirm" id="confirm-dialog-confirm">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Confirm</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Search Overlay -->
    <div class="mobile-search-overlay" id="mobile-search-overlay">
        <div class="mobile-search-container">
            <div class="mobile-search-header">
                <button class="mobile-search-back" id="mobile-search-back">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="mobile-search-input-container">
                    <input type="text" class="mobile-search-input" id="mobile-search-input"
                           placeholder="Search students..." autocomplete="off">
                </div>
            </div>
        </div>
        <div class="mobile-search-results" id="mobile-search-results">
            <!-- Search results will be populated here -->
        </div>
    </div>

    <!-- QR Code Scanner Modal -->
    <div class="modal-overlay" id="qr-scanner-overlay">
        <div class="modal qr-scanner-modal" id="qr-scanner-modal">
            <div class="modal-header">
                <h2 class="modal-title">QR Code Scanner</h2>
                <button class="modal-close" id="qr-scanner-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content qr-scanner-content">
                <div id="qr-reader"></div>
                <div class="qr-scanner-message" id="qr-scanner-message">
                    <div class="qr-scanner-icon">
                        <span class="material-icons">qr_code_scanner</span>
                    </div>
                    <div class="qr-scanner-text">Position the QR code within the frame to scan</div>

                    <!-- Development Test Button -->
                    <div class="qr-test-option" style="margin-top: 20px; display: none;" id="qr-test-option">
                        <div style="margin-bottom: 12px; color: var(--text-secondary); font-size: 14px;">
                            Development Test:
                        </div>
                        <button class="mdc-button mdc-button--outlined" id="qr-test-btn">
                            <span class="mdc-button__ripple"></span>
                            <span class="material-icons mdc-button__icon">bug_report</span>
                            <span class="mdc-button__label">Test QR Scan</span>
                        </button>
                    </div>
                </div>
                <div class="qr-scanner-result" id="qr-scanner-result" style="display: none;">
                    <div class="qr-result-icon">
                        <span class="material-icons">check_circle</span>
                    </div>
                    <div class="qr-result-title">QR Code Scanned Successfully!</div>
                    <div class="qr-result-content" id="qr-result-content"></div>
                </div>
            </div>
            <div class="modal-actions">
                <button type="button" class="mdc-button mdc-button--outlined" id="qr-scanner-cancel">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Cancel</span>
                </button>
                <button type="button" class="mdc-button mdc-button--raised" id="qr-scanner-ok" style="display: none;">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">OK</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Filter Offcanvas Component -->
    <div class="offcanvas-overlay" id="filter-offcanvas-overlay">
        <div class="offcanvas" id="filter-offcanvas">
            <div class="offcanvas-header">
                <div class="offcanvas-title">Filter Students</div>
                <button class="offcanvas-close" id="filter-offcanvas-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="offcanvas-content">
                <!-- Filter Form -->
                <div class="filter-form">
                    <!-- Grade Filter -->
                    <div class="filter-group">
                        <label class="filter-label">Grade</label>
                        <div class="mdc-select mdc-select--outlined filter-select" id="grade-filter-select">
                            <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                                <span class="mdc-select__selected-text-container">
                                    <span class="mdc-select__selected-text">All Grades</span>
                                </span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                                <div class="mdc-notched-outline">
                                    <div class="mdc-notched-outline__leading"></div>
                                    <div class="mdc-notched-outline__trailing"></div>
                                </div>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-deprecated-list" role="listbox">
                                    <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="" role="option" aria-selected="true">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">All Grades</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="Grade 9" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">Grade 9</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="Grade 10" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">Grade 10</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="Grade 11" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">Grade 11</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="Grade 12" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">Grade 12</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Gender Filter -->
                    <div class="filter-group">
                        <label class="filter-label">Gender</label>
                        <div class="mdc-select mdc-select--outlined filter-select" id="gender-filter-select">
                            <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                                <span class="mdc-select__selected-text-container">
                                    <span class="mdc-select__selected-text">All Genders</span>
                                </span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                                <div class="mdc-notched-outline">
                                    <div class="mdc-notched-outline__leading"></div>
                                    <div class="mdc-notched-outline__trailing"></div>
                                </div>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-deprecated-list" role="listbox">
                                    <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="" role="option" aria-selected="true">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">All Genders</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="Male" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">Male</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="Female" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">Female</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Birth Year Filter -->
                    <div class="filter-group">
                        <label class="filter-label">Birth Year</label>
                        <div class="mdc-select mdc-select--outlined filter-select" id="birth-year-filter-select">
                            <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                                <span class="mdc-select__selected-text-container">
                                    <span class="mdc-select__selected-text">All Years</span>
                                </span>
                                <span class="mdc-select__dropdown-icon">
                                    <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                                        <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                        <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                    </svg>
                                </span>
                                <div class="mdc-notched-outline">
                                    <div class="mdc-notched-outline__leading"></div>
                                    <div class="mdc-notched-outline__trailing"></div>
                                </div>
                            </div>
                            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                                <ul class="mdc-deprecated-list" role="listbox">
                                    <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="" role="option" aria-selected="true">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">All Years</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="2006" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">2006</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="2007" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">2007</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="2008" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">2008</span>
                                    </li>
                                    <li class="mdc-deprecated-list-item" data-value="2009" role="option">
                                        <span class="mdc-deprecated-list-item__ripple"></span>
                                        <span class="mdc-deprecated-list-item__text">2009</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Date Added Filter -->
                    <div class="filter-group">
                        <label class="filter-label">Date Added</label>
                        <div class="date-filter-container">
                            <div class="mdc-text-field mdc-text-field--outlined filter-date-input">
                                <input type="text" class="mdc-text-field__input" id="date-from-filter" placeholder="From date" readonly>
                                <div class="mdc-notched-outline">
                                    <div class="mdc-notched-outline__leading"></div>
                                    <div class="mdc-notched-outline__notch">
                                        <label for="date-from-filter" class="mdc-floating-label">From Date</label>
                                    </div>
                                    <div class="mdc-notched-outline__trailing"></div>
                                </div>
                            </div>
                            <div class="date-separator">to</div>
                            <div class="mdc-text-field mdc-text-field--outlined filter-date-input">
                                <input type="text" class="mdc-text-field__input" id="date-to-filter" placeholder="To date" readonly>
                                <div class="mdc-notched-outline">
                                    <div class="mdc-notched-outline__leading"></div>
                                    <div class="mdc-notched-outline__notch">
                                        <label for="date-to-filter" class="mdc-floating-label">To Date</label>
                                    </div>
                                    <div class="mdc-notched-outline__trailing"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Clear Filters Button -->
                    <div class="filter-actions">
                        <button class="mdc-button mdc-button--outlined" id="clear-filters-btn">
                            <span class="mdc-button__ripple"></span>
                            <span class="material-icons mdc-button__icon">clear</span>
                            <span class="mdc-button__label">Clear All Filters</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Flatpickr JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <!-- Material Design JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.js"></script>

    <!-- HTML5 QR Code Scanner -->
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>

    <!-- Alpine.js -->

    <!-- QR Scanner JavaScript -->
    <script src="js/qr-scanner.js"></script>

    <!-- Custom JavaScript -->
    <script src="script.js"></script>
</body>
</html>