# Material Design Modal HTMX Integration

This document explains the automatic modal behavior system that mimics the old Bootstrap modal functionality but with Material Design components.

## Overview

The system provides automatic modal show/hide behavior similar to the old Bootstrap modal system:
- **Automatically shows modal** when HTMX targets modal content
- **Automatically hides modal** on 204 status responses  
- **Initializes Material Design components** after content swap
- **Handles modal cleanup** on hide

## How It Works

### Automatic Modal Show
When an HTMX request targets a modal content area (any element with ID ending in `-content` or specific IDs like `dialog`, `dialog-xl`), the modal automatically shows.

```html
<!-- This will automatically show the modal -->
<button hx-get="/some-endpoint/" hx-target="#student-modal-content">
    Add Student
</button>
```

### Automatic Modal Hide
The modal automatically hides in these scenarios:
- **204 (No Content) response**: Action completed successfully
- **201 (Created) response**: Resource created successfully  
- **Empty response**: No content to display

```python
# Django view - this will auto-hide the modal
def save_student(request):
    # ... save logic ...
    return HttpResponse(status=204)  # Modal closes automatically
```

### Material Design Component Initialization
All Material Design components are automatically initialized when content is swapped into the modal:
- Text fields
- Select fields  
- Checkboxes
- Radio buttons
- Switches
- Data tables

## Usage Examples

### Basic Modal Usage

1. **Include the modal template:**
```html
{% include 'material/modal.html' with modal_id='student-modal' modal_title='Add Student' %}
```

2. **Trigger with HTMX:**
```html
<button class="mdc-button mdc-button--raised" 
        hx-get="{% url 'student_add' %}" 
        hx-target="#student-modal-content">
    <span class="mdc-button__label">Add Student</span>
</button>
```

3. **Django view returns form:**
```python
def student_add(request):
    if request.method == 'POST':
        # Process form
        if form.is_valid():
            form.save()
            return HttpResponse(status=204)  # Auto-hide modal
    else:
        form = StudentForm()
    
    return render(request, 'student_form.html', {'form': form})
```

### Updated Scolarite Template

The scolarite template has been updated to use Material Design inputs:

```html
<!-- Material Design Text Field -->
<div class="mdc-text-field mdc-text-field--outlined">
    <input class="mdc-text-field__input" type="number" id="id_year_fees" name="year_fees">
    <div class="mdc-notched-outline">
        <div class="mdc-notched-outline__leading"></div>
        <div class="mdc-notched-outline__notch">
            <label class="mdc-floating-label" for="id_year_fees">Scolarité</label>
        </div>
        <div class="mdc-notched-outline__trailing"></div>
    </div>
</div>

<!-- Material Design Select -->
<div class="mdc-select mdc-select--outlined">
    <div class="mdc-select__anchor" hx-get="/versements/frais_scolarite/" hx-target="#id_year_fees">
        <!-- Select structure -->
    </div>
</div>
```

## Modal Template Parameters

```html
{% include 'material/modal.html' with 
    modal_id='my-modal'           <!-- Required: Unique modal ID -->
    modal_title='Modal Title'     <!-- Required: Modal header title -->
    modal_size='large'           <!-- Optional: small, medium, large, xl -->
    submit_text='Save'           <!-- Optional: Submit button text -->
    cancel_text='Cancel'         <!-- Optional: Cancel button text -->
    hide_footer=False            <!-- Optional: Hide modal footer -->
%}
```

## Manual Modal Control

You can also control modals manually:

```javascript
// Show modal
window.MaterialModalHTMX.show('student-modal');

// Hide modal  
window.MaterialModalHTMX.hide('student-modal');

// Clear modal content
window.MaterialModalHTMX.clear('student-modal');

// Initialize components in container
window.MaterialModalHTMX.initializeComponents(document.getElementById('some-container'));

// Using modal-specific functions
studentModalModal.show();
studentModalModal.hide();
studentModalModal.setTitle('New Title');
```

## Migration from Old System

### Before (Bootstrap)
```html
<a href="#" hx-get="{% url 'student_add' %}" hx-target="#dialog">Add Student</a>
```

### After (Material Design)
```html
<button class="mdc-button mdc-button--raised" 
        hx-get="{% url 'student_add' %}" 
        hx-target="#student-modal-content">
    <span class="mdc-button__label">Add Student</span>
</button>

<!-- Include modal template -->
{% include 'material/modal.html' with modal_id='student-modal' modal_title='Add Student' %}
```

## Files Modified

1. **`school/templates/partials/student/student_fees_form.html`** - Updated to use Material Design inputs
2. **`static/material/js/modal-htmx.js`** - New HTMX integration script
3. **`templates/material/modal.html`** - Updated modal template
4. **`templates/material/base.html`** - Added modal script inclusion

## Benefits

- **Consistent UX**: Same automatic behavior as old system
- **Material Design**: Modern, accessible components
- **Reusable**: One script handles all modals
- **Maintainable**: Centralized modal logic
- **Automatic**: No need to write show/hide JavaScript for each modal

## Troubleshooting

### Modal doesn't show
- Check that HTMX target matches modal content ID pattern (`*-content`)
- Ensure modal template is included in the page
- Verify modal-htmx.js is loaded

### Components not initialized
- Check browser console for JavaScript errors
- Ensure Material Design JavaScript is loaded before modal script
- Verify component HTML structure matches Material Design specs

### Modal doesn't hide on form submission
- Ensure server returns 204 or 201 status code
- Check that HTMX request targets modal content area
- Verify no JavaScript errors preventing event handling
