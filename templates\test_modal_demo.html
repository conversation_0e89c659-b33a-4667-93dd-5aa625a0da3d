<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Material Modal HTMX Demo</title>
    
    <!-- Material Design CSS -->
    <link href="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="/static/material/css/styles.css">
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Material Design JS -->
    <script src="https://unpkg.com/material-components-web@latest/dist/material-components-web.min.js"></script>
</head>
<body>
    <div class="container" style="padding: 40px;">
        <h1>Material Modal HTMX Integration Demo</h1>
        
        <p>This demonstrates the automatic modal behavior where:</p>
        <ul>
            <li>Modal shows automatically when HTMX targets modal content</li>
            <li>Modal hides automatically on 204 status responses</li>
            <li>Material Design components are initialized automatically</li>
        </ul>
        
        <div style="margin: 20px 0;">
            <button class="mdc-button mdc-button--raised" 
                    hx-get="/versements/frais_scolarite/" 
                    hx-target="#test-modal-content">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Load Scolarite Form</span>
            </button>
            
            <button class="mdc-button mdc-button--outlined" 
                    onclick="testModalModal.show()">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Show Modal Manually</span>
            </button>
            
            <button class="mdc-button mdc-button--outlined" 
                    onclick="testModalModal.hide()">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Hide Modal Manually</span>
            </button>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>Test Different Status Codes:</h3>
            <button class="mdc-button mdc-button--outlined" 
                    hx-post="/test/204" 
                    hx-target="#test-modal-content">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Test 204 Response (Auto Hide)</span>
            </button>
            
            <button class="mdc-button mdc-button--outlined" 
                    hx-post="/test/201" 
                    hx-target="#test-modal-content">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Test 201 Response (Auto Hide)</span>
            </button>
        </div>
    </div>

    <!-- Include the Material Modal -->
    {% include 'material_modal.html' with modal_id='test-modal' modal_title='Test Modal' modal_size='large' %}

    <!-- Include our HTMX Modal Integration Script -->
    <script src="/static/material/js/modal-htmx.js"></script>
    
    <script>
        // Initialize Material Design components
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize buttons
            document.querySelectorAll('.mdc-button').forEach(button => {
                if (!button.mdcRipple) {
                    button.mdcRipple = new mdc.ripple.MDCRipple(button);
                }
            });
        });
        
        // Test functions
        function testManualShow() {
            // Set some test content
            const testContent = `
                <div class="form-field">
                    <div class="mdc-text-field mdc-text-field--outlined">
                        <input class="mdc-text-field__input" type="text" id="test-input">
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch">
                                <label class="mdc-floating-label" for="test-input">Test Input</label>
                            </div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>
                <div class="form-field">
                    <div class="mdc-select mdc-select--outlined">
                        <div class="mdc-select__anchor">
                            <span class="mdc-notched-outline">
                                <span class="mdc-notched-outline__leading"></span>
                                <span class="mdc-notched-outline__notch">
                                    <span class="mdc-floating-label">Test Select</span>
                                </span>
                                <span class="mdc-notched-outline__trailing"></span>
                            </span>
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text"></span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <span class="material-icons">arrow_drop_down</span>
                            </span>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list">
                                <li class="mdc-deprecated-list-item" data-value="option1">
                                    <span class="mdc-deprecated-list-item__text">Option 1</span>
                                </li>
                                <li class="mdc-deprecated-list-item" data-value="option2">
                                    <span class="mdc-deprecated-list-item__text">Option 2</span>
                                </li>
                            </ul>
                        </div>
                        <select style="display: none;">
                            <option value="option1">Option 1</option>
                            <option value="option2">Option 2</option>
                        </select>
                    </div>
                </div>
            `;
            
            document.getElementById('test-modal-content').innerHTML = testContent;
            window.MaterialModalHTMX.initializeComponents(document.getElementById('test-modal-content'));
            testModalModal.show();
        }
    </script>
</body>
</html>
