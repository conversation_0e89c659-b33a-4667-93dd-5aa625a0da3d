"""
Demo views for testing MDC template tags
"""

from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from .forms_demo import MDCDemoForm, SimpleStudentForm


def mdc_form_demo(request):
    """Demo view for MDC form fields"""
    form = MDCDemoForm()
    
    if request.method == 'POST':
        form = MDCDemoForm(request.POST)
        if form.is_valid():
            # Process form data
            return HttpResponse("Form submitted successfully!", status=200)
    
    return render(request, 'material/mdc_form_demo.html', {'form': form})


def simple_student_form_demo(request):
    """Simple student form demo"""
    form = SimpleStudentForm()
    
    if request.method == 'POST':
        form = SimpleStudentForm(request.POST)
        if form.is_valid():
            return HttpResponse(status=204)  # Auto-close modal
    
    # For HTMX requests, return just the form content
    if request.htmx:
        return render(request, 'material/simple_student_form.html', {'form': form})
    
    return render(request, 'material/student_form_demo_page.html', {'form': form})


@require_http_methods(["GET"])
def check_student_htmx(request):
    """HTMX endpoint to check student info"""
    student_id = request.GET.get('student_id', '')
    
    if not student_id:
        return HttpResponse('')
    
    # Simulate student lookup
    if len(student_id) >= 3:
        student_info = {
            'name': f'Student {student_id}',
            'email': f'student{student_id}@school.com',
            'status': 'Active'
        }
        
        html = f'''
        <div class="student-info-card">
            <h4>Student Found</h4>
            <p><strong>Name:</strong> {student_info['name']}</p>
            <p><strong>Email:</strong> {student_info['email']}</p>
            <p><strong>Status:</strong> {student_info['status']}</p>
        </div>
        '''
        return HttpResponse(html)
    else:
        return HttpResponse('<div class="text-muted">Enter at least 3 characters</div>')


@require_http_methods(["GET"])
def get_fees_htmx(request):
    """HTMX endpoint to get fees based on level"""
    level = request.GET.get('level', '')
    
    if not level:
        return HttpResponse('')
    
    # Simulate fee calculation
    fees = {
        '1': {'enrollment': 50000, 'tuition': 200000, 'annexe': 25000},
        '2': {'enrollment': 55000, 'tuition': 220000, 'annexe': 27500},
        '3': {'enrollment': 60000, 'tuition': 240000, 'annexe': 30000},
        '4': {'enrollment': 65000, 'tuition': 260000, 'annexe': 32500},
        '5': {'enrollment': 70000, 'tuition': 280000, 'annexe': 35000},
    }
    
    level_fees = fees.get(level, {})
    
    if level_fees:
        html = f'''
        <div class="fees-info-card">
            <h4>Level {level} Fees</h4>
            <div class="fee-item">
                <span>Enrollment:</span>
                <span>{level_fees['enrollment']:,} CFA</span>
            </div>
            <div class="fee-item">
                <span>Tuition:</span>
                <span>{level_fees['tuition']:,} CFA</span>
            </div>
            <div class="fee-item">
                <span>Annexe:</span>
                <span>{level_fees['annexe']:,} CFA</span>
            </div>
            <div class="fee-total">
                <strong>Total: {sum(level_fees.values()):,} CFA</strong>
            </div>
        </div>
        '''
        return HttpResponse(html)
    else:
        return HttpResponse('<div class="text-muted">No fees found for this level</div>')


@require_http_methods(["GET"])
def get_cities_htmx(request):
    """HTMX endpoint to get cities based on country"""
    country = request.GET.get('country', '')
    
    cities = {
        'US': ['New York', 'Los Angeles', 'Chicago', 'Houston'],
        'CA': ['Toronto', 'Vancouver', 'Montreal', 'Calgary'],
        'UK': ['London', 'Manchester', 'Birmingham', 'Liverpool'],
        'FR': ['Paris', 'Lyon', 'Marseille', 'Toulouse'],
        'DE': ['Berlin', 'Munich', 'Hamburg', 'Cologne'],
        'JP': ['Tokyo', 'Osaka', 'Kyoto', 'Yokohama'],
        'AU': ['Sydney', 'Melbourne', 'Brisbane', 'Perth'],
    }
    
    country_cities = cities.get(country, [])
    
    if country_cities:
        options = ''.join([f'<option value="{city}">{city}</option>' for city in country_cities])
        html = f'''
        <div class="mdc-select mdc-select--outlined" id="city-field">
            <div class="mdc-select__anchor">
                <span class="mdc-notched-outline">
                    <span class="mdc-notched-outline__leading"></span>
                    <span class="mdc-notched-outline__notch">
                        <span class="mdc-floating-label">City</span>
                    </span>
                    <span class="mdc-notched-outline__trailing"></span>
                </span>
                <span class="mdc-select__selected-text-container">
                    <span class="mdc-select__selected-text"></span>
                </span>
                <span class="mdc-select__dropdown-icon">
                    <span class="material-icons">arrow_drop_down</span>
                </span>
            </div>
            <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                <ul class="mdc-deprecated-list">
                    <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="">
                        <span class="mdc-deprecated-list-item__text">--------</span>
                    </li>
                    {' '.join([f'<li class="mdc-deprecated-list-item" data-value="{city}"><span class="mdc-deprecated-list-item__text">{city}</span></li>' for city in country_cities])}
                </ul>
            </div>
            <select name="city" style="display: none;">
                <option value="">--------</option>
                {options}
            </select>
        </div>
        '''
        return HttpResponse(html)
    else:
        return HttpResponse('')


# Test endpoints for modal behavior
@require_http_methods(["POST"])
def test_204_response(request):
    """Test endpoint that returns 204 (should auto-close modal)"""
    return HttpResponse(status=204)


@require_http_methods(["POST"]) 
def test_201_response(request):
    """Test endpoint that returns 201 (should auto-close modal)"""
    return HttpResponse(status=201)
