# Material Design Table Components

This directory contains reusable JavaScript components for Material Design tables with advanced functionality.

## Components

### 1. TableRowSelection (`table-row-selection.js`)

A generic component for handling row selection in Material Design data tables.

**Features:**
- Header checkbox for select all/none functionality
- Individual row checkboxes
- Click-to-select rows (excluding action buttons)
- Visual feedback for selected rows
- Indeterminate state for partial selections
- Configurable selectors and callbacks

**Basic Usage:**
```javascript
const tableSelection = new TableRowSelection({
    tableSelector: '.mdc-data-table',
    headerCheckboxSelector: '#select-all-checkbox',
    rowCheckboxSelector: '.row-checkbox',
    rowSelector: '.mdc-data-table__row:not(.mdc-data-table__header-row)',
    onSelectionChange: function(selectedRows) {
        console.log('Selected:', selectedRows);
    }
});
```

### 2. FloatingActions (`floating-actions.js`)

A reusable floating action bar that appears when items are selected, providing bulk operations.

**Features:**
- Configurable actions with icons and labels
- Smooth show/hide animations
- Mobile-responsive positioning
- Custom action handlers
- Dynamic action updates
- Keyboard support (ESC to hide)

**Basic Usage:**
```javascript
const floatingActions = new FloatingActions({
    containerId: 'my-floating-actions',
    actions: [
        {
            id: 'edit',
            icon: 'edit',
            label: 'Edit',
            type: 'archive',
            handler: function(count, actionId) {
                console.log(`Edit ${count} items`);
            }
        },
        {
            id: 'delete',
            icon: 'delete',
            label: 'Delete',
            type: 'delete',
            handler: function(count, actionId) {
                console.log(`Delete ${count} items`);
            }
        }
    ]
});

// Show with selected count
floatingActions.show(5);

// Hide
floatingActions.hide();
```

## Integration Example

Here's how to use both components together:

```javascript
// Initialize floating actions
const floatingActions = new FloatingActions({
    containerId: 'table-floating-actions',
    actions: [
        { id: 'edit', icon: 'edit', label: 'Edit', type: 'archive' },
        { id: 'delete', icon: 'delete', label: 'Delete', type: 'delete' }
    ]
});

// Initialize table selection
const tableSelection = new TableRowSelection({
    tableSelector: '.mdc-data-table',
    onSelectionChange: function(selectedRows) {
        if (selectedRows.length > 0) {
            floatingActions.show(selectedRows.length);
        } else {
            floatingActions.hide();
        }
    }
});
```

## Required HTML Structure

### Table Structure
```html
<div class="mdc-data-table">
    <div class="mdc-data-table__table-container">
        <table class="mdc-data-table__table">
            <thead>
                <tr class="mdc-data-table__header-row">
                    <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox">
                        <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                            <input type="checkbox" class="mdc-checkbox__native-control" id="select-all-checkbox">
                            <!-- checkbox markup -->
                        </div>
                    </th>
                    <!-- other headers -->
                </tr>
            </thead>
            <tbody class="mdc-data-table__content">
                <tr class="mdc-data-table__row" data-student-id="1">
                    <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                        <div class="mdc-checkbox mdc-data-table__row-checkbox">
                            <input type="checkbox" class="mdc-checkbox__native-control row-checkbox">
                            <!-- checkbox markup -->
                        </div>
                    </td>
                    <!-- other cells -->
                </tr>
            </tbody>
        </table>
    </div>
</div>
```

## CSS Classes

The components use existing Material Design CSS classes. Make sure you have:

- Material Design Components CSS
- Custom bulk action bar styles (already included in `styles.css`)
- Table selection styles (already included in `styles.css`)

## Action Types

The floating actions support different visual types:

- `archive` - Gray background, suitable for non-destructive actions
- `delete` - Red background, suitable for destructive actions
- Custom types can be added by extending the CSS

## Mobile Support

Both components are fully responsive:

- Table selection works on touch devices
- Floating actions adapt to mobile layouts
- Actions are positioned above bottom navigation on mobile

## Browser Support

- Modern browsers with ES6 support
- IE11+ (with polyfills for ES6 features)
- Mobile browsers (iOS Safari, Chrome Mobile, etc.)

## Dependencies

- Material Design Components (MDC) for styling
- No external JavaScript dependencies

## Examples

See `floating-actions-examples.js` for comprehensive usage examples including:

- Basic usage
- Advanced configurations
- Dynamic actions
- Mobile optimization
- Integration patterns
- Preset configurations for different contexts
