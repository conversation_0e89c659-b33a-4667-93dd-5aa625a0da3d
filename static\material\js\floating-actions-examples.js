/**
 * Floating Actions Component - Usage Examples
 * 
 * This file contains examples of how to use the FloatingActions component
 * in different scenarios throughout the application.
 */

// Example 1: Basic usage with simple actions
function createBasicFloatingActions() {
    const floatingActions = new FloatingActions({
        containerId: 'basic-floating-actions',
        actions: [
            {
                id: 'edit',
                icon: 'edit',
                label: 'Edit',
                type: 'archive',
                handler: function(count, actionId) {
                    console.log(`Edit ${count} items`);
                }
            },
            {
                id: 'delete',
                icon: 'delete',
                label: 'Delete',
                type: 'delete',
                handler: function(count, actionId) {
                    console.log(`Delete ${count} items`);
                }
            }
        ]
    });
    
    return floatingActions;
}

// Example 2: Advanced usage with custom callbacks
function createAdvancedFloatingActions() {
    const floatingActions = new FloatingActions({
        containerId: 'advanced-floating-actions',
        actions: [
            {
                id: 'approve',
                icon: 'check',
                label: 'Approve',
                type: 'archive',
                tooltip: 'Approve selected items'
            },
            {
                id: 'reject',
                icon: 'close',
                label: 'Reject',
                type: 'delete',
                tooltip: 'Reject selected items'
            },
            {
                id: 'export',
                icon: 'download',
                label: 'Export',
                type: 'archive',
                tooltip: 'Export selected items'
            }
        ],
        onShow: function(count) {
            console.log(`Floating actions shown for ${count} items`);
            // Update page state when actions are shown
            document.body.classList.add('bulk-actions-active');
        },
        onHide: function() {
            console.log('Floating actions hidden');
            // Update page state when actions are hidden
            document.body.classList.remove('bulk-actions-active');
        },
        onActionClick: function(actionId, count) {
            // Handle all action clicks in one place
            switch(actionId) {
                case 'approve':
                    handleApprove(count);
                    break;
                case 'reject':
                    handleReject(count);
                    break;
                case 'export':
                    handleExport(count);
                    break;
            }
        }
    });
    
    return floatingActions;
}

// Example 3: Integration with table row selection
function integrateWithTableSelection() {
    // Create floating actions
    const floatingActions = new FloatingActions({
        containerId: 'table-floating-actions',
        actions: [
            {
                id: 'bulk-edit',
                icon: 'edit',
                label: 'Edit Selected',
                type: 'archive'
            },
            {
                id: 'bulk-delete',
                icon: 'delete',
                label: 'Delete Selected',
                type: 'delete'
            }
        ]
    });
    
    // Create table selection
    const tableSelection = new TableRowSelection({
        tableSelector: '.data-table',
        onSelectionChange: function(selectedRows) {
            if (selectedRows.length > 0) {
                floatingActions.show(selectedRows.length);
            } else {
                floatingActions.hide();
            }
        }
    });
    
    return { floatingActions, tableSelection };
}

// Example 4: Dynamic actions based on selection
function createDynamicFloatingActions() {
    const floatingActions = new FloatingActions({
        containerId: 'dynamic-floating-actions',
        actions: [] // Start with no actions
    });
    
    // Function to update actions based on selection context
    function updateActionsForContext(selectedItems, context) {
        let actions = [];
        
        if (context === 'students') {
            actions = [
                { id: 'edit', icon: 'edit', label: 'Edit', type: 'archive' },
                { id: 'export', icon: 'download', label: 'Export', type: 'archive' },
                { id: 'archive', icon: 'archive', label: 'Archive', type: 'archive' },
                { id: 'delete', icon: 'delete', label: 'Delete', type: 'delete' }
            ];
        } else if (context === 'payments') {
            actions = [
                { id: 'approve', icon: 'check', label: 'Approve', type: 'archive' },
                { id: 'reject', icon: 'close', label: 'Reject', type: 'delete' },
                { id: 'export', icon: 'download', label: 'Export', type: 'archive' }
            ];
        } else if (context === 'grades') {
            actions = [
                { id: 'export', icon: 'download', label: 'Export', type: 'archive' },
                { id: 'print', icon: 'print', label: 'Print', type: 'archive' }
            ];
        }
        
        floatingActions.setActions(actions);
        
        if (selectedItems.length > 0) {
            floatingActions.show(selectedItems.length);
        } else {
            floatingActions.hide();
        }
    }
    
    return { floatingActions, updateActionsForContext };
}

// Example 5: Mobile-optimized floating actions
function createMobileOptimizedFloatingActions() {
    const floatingActions = new FloatingActions({
        containerId: 'mobile-floating-actions',
        mobilePosition: 'bottom-full', // Full width on mobile
        actions: [
            {
                id: 'share',
                icon: 'share',
                label: 'Share',
                type: 'archive'
            },
            {
                id: 'favorite',
                icon: 'favorite',
                label: 'Favorite',
                type: 'archive'
            },
            {
                id: 'delete',
                icon: 'delete',
                label: 'Delete',
                type: 'delete'
            }
        ]
    });
    
    return floatingActions;
}

// Example helper functions for action handlers
function handleApprove(count) {
    if (confirm(`Approve ${count} item${count > 1 ? 's' : ''}?`)) {
        // Implement approval logic
        console.log(`Approving ${count} items`);
    }
}

function handleReject(count) {
    if (confirm(`Reject ${count} item${count > 1 ? 's' : ''}?`)) {
        // Implement rejection logic
        console.log(`Rejecting ${count} items`);
    }
}

function handleExport(count) {
    // Implement export logic
    console.log(`Exporting ${count} items`);
    // Example: trigger download
    // window.location.href = `/export?ids=${selectedIds.join(',')}`;
}

// Example: How to use with different table types
const FloatingActionsPresets = {
    students: {
        actions: [
            { id: 'edit', icon: 'edit', label: 'Modifier', type: 'archive' },
            { id: 'export', icon: 'download', label: 'Exporter', type: 'archive' },
            { id: 'archive', icon: 'archive', label: 'Archiver', type: 'archive' },
            { id: 'delete', icon: 'delete', label: 'Supprimer', type: 'delete' }
        ]
    },
    
    payments: {
        actions: [
            { id: 'approve', icon: 'check', label: 'Approuver', type: 'archive' },
            { id: 'reject', icon: 'close', label: 'Rejeter', type: 'delete' },
            { id: 'export', icon: 'download', label: 'Exporter', type: 'archive' }
        ]
    },
    
    grades: {
        actions: [
            { id: 'export', icon: 'download', label: 'Exporter', type: 'archive' },
            { id: 'print', icon: 'print', label: 'Imprimer', type: 'archive' },
            { id: 'email', icon: 'email', label: 'Envoyer', type: 'archive' }
        ]
    },
    
    attendance: {
        actions: [
            { id: 'mark-present', icon: 'check', label: 'Présent', type: 'archive' },
            { id: 'mark-absent', icon: 'close', label: 'Absent', type: 'delete' },
            { id: 'export', icon: 'download', label: 'Exporter', type: 'archive' }
        ]
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        createBasicFloatingActions,
        createAdvancedFloatingActions,
        integrateWithTableSelection,
        createDynamicFloatingActions,
        createMobileOptimizedFloatingActions,
        FloatingActionsPresets
    };
} else {
    window.FloatingActionsExamples = {
        createBasicFloatingActions,
        createAdvancedFloatingActions,
        integrateWithTableSelection,
        createDynamicFloatingActions,
        createMobileOptimizedFloatingActions,
        FloatingActionsPresets
    };
}
