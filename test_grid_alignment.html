<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grid Alignment Test</title>
    <link rel="stylesheet" href="static/css/material/styles.css">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-section {
            margin-bottom: 40px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .col-demo {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 10px;
            text-align: center;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .form-field {
            margin-bottom: 16px;
        }
        .form-field label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
        }
        .form-field input, .form-field select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <h1>Grid System Alignment Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Basic 12-Column Grid</h3>
        <div class="row">
            <div class="col-1"><div class="col-demo">1</div></div>
            <div class="col-1"><div class="col-demo">1</div></div>
            <div class="col-1"><div class="col-demo">1</div></div>
            <div class="col-1"><div class="col-demo">1</div></div>
            <div class="col-1"><div class="col-demo">1</div></div>
            <div class="col-1"><div class="col-demo">1</div></div>
            <div class="col-1"><div class="col-demo">1</div></div>
            <div class="col-1"><div class="col-demo">1</div></div>
            <div class="col-1"><div class="col-demo">1</div></div>
            <div class="col-1"><div class="col-demo">1</div></div>
            <div class="col-1"><div class="col-demo">1</div></div>
            <div class="col-1"><div class="col-demo">1</div></div>
        </div>
    </div>

    <div class="test-section">
        <h3>Test 2: Mixed Column Sizes</h3>
        <div class="row">
            <div class="col-3"><div class="col-demo">col-3</div></div>
            <div class="col-6"><div class="col-demo">col-6</div></div>
            <div class="col-3"><div class="col-demo">col-3</div></div>
        </div>
        <div class="row">
            <div class="col-4"><div class="col-demo">col-4</div></div>
            <div class="col-4"><div class="col-demo">col-4</div></div>
            <div class="col-4"><div class="col-demo">col-4</div></div>
        </div>
        <div class="row">
            <div class="col-2"><div class="col-demo">col-2</div></div>
            <div class="col-8"><div class="col-demo">col-8</div></div>
            <div class="col-2"><div class="col-demo">col-2</div></div>
        </div>
    </div>

    <div class="test-section">
        <h3>Test 3: Form Fields Alignment</h3>
        <div class="row">
            <div class="col-6">
                <div class="form-field">
                    <label for="first_name">First Name</label>
                    <input type="text" id="first_name" name="first_name" placeholder="Enter first name">
                </div>
            </div>
            <div class="col-6">
                <div class="form-field">
                    <label for="last_name">Last Name</label>
                    <input type="text" id="last_name" name="last_name" placeholder="Enter last name">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-4">
                <div class="form-field">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" placeholder="Enter email">
                </div>
            </div>
            <div class="col-4">
                <div class="form-field">
                    <label for="phone">Phone</label>
                    <input type="tel" id="phone" name="phone" placeholder="Enter phone">
                </div>
            </div>
            <div class="col-4">
                <div class="form-field">
                    <label for="country">Country</label>
                    <select id="country" name="country">
                        <option value="">Select country</option>
                        <option value="us">United States</option>
                        <option value="ca">Canada</option>
                        <option value="uk">United Kingdom</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>Test 4: Responsive Columns</h3>
        <div class="row">
            <div class="col-12 col-md-6 col-lg-4"><div class="col-demo">Responsive 1</div></div>
            <div class="col-12 col-md-6 col-lg-4"><div class="col-demo">Responsive 2</div></div>
            <div class="col-12 col-md-12 col-lg-4"><div class="col-demo">Responsive 3</div></div>
        </div>
    </div>

    <div class="test-section">
        <h3>Test 5: Offset Classes</h3>
        <div class="row">
            <div class="col-4 offset-2"><div class="col-demo">col-4 offset-2</div></div>
            <div class="col-4"><div class="col-demo">col-4</div></div>
        </div>
        <div class="row">
            <div class="col-3 offset-3"><div class="col-demo">col-3 offset-3</div></div>
            <div class="col-3 offset-3"><div class="col-demo">col-3 offset-3</div></div>
        </div>
    </div>

    <p><strong>Instructions:</strong> Open this file in a browser and resize the window to test alignment at different screen sizes. The columns should align properly without gaps causing misalignment.</p>
</body>
</html>
