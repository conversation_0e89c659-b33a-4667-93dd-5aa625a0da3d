/**
 * Generic Table Row Selection Manager
 * Provides reusable functionality for Material Design data tables with row selection
 */
class TableRowSelection {
    constructor(options = {}) {
        this.tableSelector = options.tableSelector || '.mdc-data-table';
        this.headerCheckboxSelector = options.headerCheckboxSelector || '#select-all-checkbox';
        this.rowCheckboxSelector = options.rowCheckboxSelector || '.row-checkbox';
        this.rowSelector = options.rowSelector || '.mdc-data-table__row';
        this.selectedRowClass = options.selectedRowClass || 'mdc-data-table__row--selected';
        this.selectedCheckboxClass = options.selectedCheckboxClass || 'mdc-checkbox--selected';
        
        // Callbacks
        this.onSelectionChange = options.onSelectionChange || null;
        this.onSelectAll = options.onSelectAll || null;
        this.onDeselectAll = options.onDeselectAll || null;
        
        // Internal state
        this.selectedRows = new Set();
        this.table = null;
        this.headerCheckbox = null;
        this.rowCheckboxes = [];
        this.rows = [];
        
        this.init();
    }
    
    init() {
        this.table = document.querySelector(this.tableSelector);
        if (!this.table) {
            console.warn('Table not found with selector:', this.tableSelector);
            return;
        }
        
        this.headerCheckbox = this.table.querySelector(this.headerCheckboxSelector);
        this.rowCheckboxes = Array.from(this.table.querySelectorAll(this.rowCheckboxSelector));
        this.rows = Array.from(this.table.querySelectorAll(this.rowSelector));
        
        this.bindEvents();
        this.updateHeaderCheckboxState();
    }
    
    bindEvents() {
        // Header checkbox (select all)
        if (this.headerCheckbox) {
            this.headerCheckbox.addEventListener('change', (e) => {
                this.handleSelectAll(e.target.checked);
            });
        }
        
        // Row checkboxes
        this.rowCheckboxes.forEach((checkbox, index) => {
            checkbox.addEventListener('change', (e) => {
                const row = this.rows[index];
                const rowId = this.getRowId(row);
                this.handleRowSelection(rowId, e.target.checked, row, checkbox);
            });
        });
        
        // Row click handling (optional - click row to select)
        this.rows.forEach((row, index) => {
            row.addEventListener('click', (e) => {
                // Don't trigger if clicking on action buttons or other interactive elements
                if (e.target.closest('.table-actions') || 
                    e.target.closest('button') || 
                    e.target.closest('a') ||
                    e.target.closest('.mdc-checkbox')) {
                    return;
                }
                
                const checkbox = this.rowCheckboxes[index];
                if (checkbox) {
                    checkbox.checked = !checkbox.checked;
                    const rowId = this.getRowId(row);
                    this.handleRowSelection(rowId, checkbox.checked, row, checkbox);
                }
            });
        });
    }
    
    getRowId(row) {
        // Try different data attributes to get row ID
        return row.dataset.studentId || 
               row.dataset.rowId || 
               row.dataset.id || 
               Array.from(this.rows).indexOf(row).toString();
    }
    
    handleSelectAll(checked) {
        this.rowCheckboxes.forEach((checkbox, index) => {
            checkbox.checked = checked;
            const row = this.rows[index];
            const rowId = this.getRowId(row);
            
            if (checked) {
                this.selectedRows.add(rowId);
                this.addRowSelectedState(row, checkbox);
            } else {
                this.selectedRows.delete(rowId);
                this.removeRowSelectedState(row, checkbox);
            }
        });
        
        this.updateHeaderCheckboxState();
        
        // Trigger callbacks
        if (checked && this.onSelectAll) {
            this.onSelectAll(Array.from(this.selectedRows));
        } else if (!checked && this.onDeselectAll) {
            this.onDeselectAll();
        }
        
        if (this.onSelectionChange) {
            this.onSelectionChange(Array.from(this.selectedRows));
        }
    }
    
    handleRowSelection(rowId, checked, row, checkbox) {
        if (checked) {
            this.selectedRows.add(rowId);
            this.addRowSelectedState(row, checkbox);
        } else {
            this.selectedRows.delete(rowId);
            this.removeRowSelectedState(row, checkbox);
        }
        
        this.updateHeaderCheckboxState();
        
        if (this.onSelectionChange) {
            this.onSelectionChange(Array.from(this.selectedRows));
        }
    }
    
    addRowSelectedState(row, checkbox) {
        row.classList.add(this.selectedRowClass);
        const checkboxContainer = checkbox.closest('.mdc-checkbox');
        if (checkboxContainer) {
            checkboxContainer.classList.add(this.selectedCheckboxClass);
        }
    }
    
    removeRowSelectedState(row, checkbox) {
        row.classList.remove(this.selectedRowClass);
        const checkboxContainer = checkbox.closest('.mdc-checkbox');
        if (checkboxContainer) {
            checkboxContainer.classList.remove(this.selectedCheckboxClass);
        }
    }
    
    updateHeaderCheckboxState() {
        if (!this.headerCheckbox) return;
        
        const totalRows = this.rowCheckboxes.length;
        const selectedCount = this.selectedRows.size;
        
        const headerCheckboxContainer = this.headerCheckbox.closest('.mdc-checkbox');
        
        if (selectedCount === 0) {
            // None selected
            this.headerCheckbox.checked = false;
            this.headerCheckbox.indeterminate = false;
            if (headerCheckboxContainer) {
                headerCheckboxContainer.classList.remove(this.selectedCheckboxClass);
            }
        } else if (selectedCount === totalRows) {
            // All selected
            this.headerCheckbox.checked = true;
            this.headerCheckbox.indeterminate = false;
            if (headerCheckboxContainer) {
                headerCheckboxContainer.classList.add(this.selectedCheckboxClass);
            }
        } else {
            // Some selected (indeterminate state)
            this.headerCheckbox.checked = false;
            this.headerCheckbox.indeterminate = true;
            if (headerCheckboxContainer) {
                headerCheckboxContainer.classList.add(this.selectedCheckboxClass);
            }
        }
    }
    
    // Public API methods
    getSelectedRows() {
        return Array.from(this.selectedRows);
    }
    
    getSelectedCount() {
        return this.selectedRows.size;
    }
    
    selectRow(rowId) {
        const rowIndex = this.rows.findIndex(row => this.getRowId(row) === rowId);
        if (rowIndex !== -1) {
            const checkbox = this.rowCheckboxes[rowIndex];
            const row = this.rows[rowIndex];
            checkbox.checked = true;
            this.handleRowSelection(rowId, true, row, checkbox);
        }
    }
    
    deselectRow(rowId) {
        const rowIndex = this.rows.findIndex(row => this.getRowId(row) === rowId);
        if (rowIndex !== -1) {
            const checkbox = this.rowCheckboxes[rowIndex];
            const row = this.rows[rowIndex];
            checkbox.checked = false;
            this.handleRowSelection(rowId, false, row, checkbox);
        }
    }
    
    selectAll() {
        this.handleSelectAll(true);
    }
    
    deselectAll() {
        this.handleSelectAll(false);
    }
    
    isRowSelected(rowId) {
        return this.selectedRows.has(rowId);
    }
    
    // Refresh the selection manager (useful when table content changes)
    refresh() {
        this.selectedRows.clear();
        this.init();
    }
    
    // Destroy the selection manager
    destroy() {
        // Remove event listeners
        if (this.headerCheckbox) {
            this.headerCheckbox.removeEventListener('change', this.handleSelectAll);
        }
        
        this.rowCheckboxes.forEach(checkbox => {
            checkbox.removeEventListener('change', this.handleRowSelection);
        });
        
        this.rows.forEach(row => {
            row.removeEventListener('click', this.handleRowSelection);
        });
        
        // Clear state
        this.selectedRows.clear();
        this.table = null;
        this.headerCheckbox = null;
        this.rowCheckboxes = [];
        this.rows = [];
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TableRowSelection;
} else {
    window.TableRowSelection = TableRowSelection;
}
