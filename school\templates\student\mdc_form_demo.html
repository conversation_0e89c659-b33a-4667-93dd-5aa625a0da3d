{% extends 'material_base.html' %}
{% load mdc_forms %}

{% block content %}
<div class="container" style="padding: 40px;">
    <h1>MDC Form Fields Demo</h1>
    <p>This demonstrates the new MDC template tags for rendering Material Design form fields.</p>
    
    <form method="post" class="demo-form">
        {% csrf_token %}
        
        <div class="form-section">
            <h3>Text Fields</h3>
            
            <div class="row">
                <div class="form-field col-md-6">
                    {% mdc_field form.first_name class="mdc-small-input" onkeyup="this.value = this.value.toUpperCase();" %}
                </div>
                <div class="form-field col-md-6">
                    {% mdc_field form.last_name class="mdc-small-input" %}
                </div>
            </div>
            
            <div class="row">
                <div class="form-field col-md-6">
                    {% mdc_field form.email type="email" %}
                </div>
                <div class="form-field col-md-6">
                    {% mdc_field form.phone type="tel" %}
                </div>
            </div>
            
            <div class="row">
                <div class="form-field col-md-4">
                    {% mdc_field form.age type="number" %}
                </div>
                <div class="form-field col-md-4">
                    {% mdc_field form.birth_date type="date" %}
                </div>
                <div class="form-field col-md-4">
                    {% mdc_field form.website type="url" %}
                </div>
            </div>
        </div>
        
        <div class="form-section">
            <h3>Select Fields</h3>
            
            <div class="row">
                <div class="form-field col-md-6">
                    {% mdc_select_field form.gender %}
                </div>
                <div class="form-field col-md-6">
                    {% mdc_select_field form.country hx_get="/get-cities/" hx_target="#city-field" hx_trigger="change" %}
                </div>
            </div>
        </div>
        
        <div class="form-section">
            <h3>Textarea Fields</h3>
            
            <div class="row">
                <div class="form-field col-md-12">
                    {% mdc_textarea_field form.bio rows="4" %}
                </div>
            </div>
            
            <div class="row">
                <div class="form-field col-md-12">
                    {% mdc_textarea_field form.notes rows="6" class="large-textarea" %}
                </div>
            </div>
        </div>
        
        <div class="form-section">
            <h3>Checkbox Fields</h3>
            
            <div class="row">
                <div class="form-field col-md-6">
                    {% mdc_checkbox_field form.newsletter %}
                </div>
                <div class="form-field col-md-6">
                    {% mdc_checkbox_field form.terms_accepted %}
                </div>
            </div>
        </div>
        
        <div class="form-section">
            <h3>HTMX Integration Examples</h3>
            
            <div class="row">
                <div class="form-field col-md-6">
                    {% mdc_field form.student_id class="mdc-small-input" hx_get="/check-student/" hx_target="#student-info" hx_trigger="keyup delay:1s" %}
                </div>
                <div class="form-field col-md-6" id="student-info">
                    <!-- Student info will be loaded here -->
                </div>
            </div>
            
            <div class="row">
                <div class="form-field col-md-6">
                    {% mdc_select_field form.level hx_get="/get-fees/" hx_target="#fees-section" hx_trigger="change" %}
                </div>
                <div class="form-field col-md-6" id="fees-section">
                    <!-- Fees will be loaded here -->
                </div>
            </div>
        </div>
        
        <div class="form-section">
            <h3>Error Handling</h3>
            <p>Fields with errors will automatically show validation messages and error styling.</p>
            
            <div class="row">
                <div class="form-field col-md-6">
                    {% mdc_field form.required_field %}
                </div>
                <div class="form-field col-md-6">
                    {% mdc_field form.email_field type="email" %}
                </div>
            </div>
        </div>
        
        <div class="form-actions">
            <button type="submit" class="mdc-button mdc-button--raised">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Submit Form</span>
            </button>
            
            <button type="reset" class="mdc-button mdc-button--outlined">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Reset</span>
            </button>
        </div>
    </form>
    
    <div class="usage-examples">
        <h2>Usage Examples</h2>
        
        <h3>Basic Usage</h3>
        <pre><code>{% verbatim %}{% load mdc_forms %}

<!-- Simple text field -->
{% mdc_field form.first_name %}

<!-- Text field with custom class -->
{% mdc_field form.last_name class="mdc-small-input" %}

<!-- Email field -->
{% mdc_field form.email type="email" %}

<!-- Select field -->
{% mdc_select_field form.country %}

<!-- Textarea -->
{% mdc_textarea_field form.bio rows="4" %}

<!-- Checkbox -->
{% mdc_checkbox_field form.newsletter %}{% endverbatim %}</code></pre>
        
        <h3>Advanced Usage with HTMX</h3>
        <pre><code>{% verbatim %}<!-- Field with HTMX integration -->
{% mdc_field form.student_id
    class="mdc-small-input"
    hx_get="/check-student/"
    hx_target="#student-info"
    hx_trigger="keyup delay:1s" %}

<!-- Select with dynamic loading -->
{% mdc_select_field form.level
    hx_get="/get-fees/"
    hx_target="#fees-section"
    hx_trigger="change" %}

<!-- Field with JavaScript events -->
{% mdc_field form.name
    onkeyup="this.value = this.value.toUpperCase();"
    onfocus="console.log('Field focused')" %}{% endverbatim %}</code></pre>
        
        <h3>Available Template Tags</h3>
        <ul>
            <li><code>{% verbatim %}{% mdc_field %}{% endverbatim %}</code> - Auto-detects field type and renders appropriate component</li>
            <li><code>{% verbatim %}{% mdc_text_field %}{% endverbatim %}</code> - Specifically renders as text field</li>
            <li><code>{% verbatim %}{% mdc_select_field %}{% endverbatim %}</code> - Specifically renders as select field</li>
            <li><code>{% verbatim %}{% mdc_textarea_field %}{% endverbatim %}</code> - Specifically renders as textarea</li>
            <li><code>{% verbatim %}{% mdc_checkbox_field %}{% endverbatim %}</code> - Specifically renders as checkbox</li>
        </ul>
        
        <h3>Supported Attributes</h3>
        <ul>
            <li><strong>class</strong> - Additional CSS classes</li>
            <li><strong>type</strong> - Input type (text, email, number, date, etc.)</li>
            <li><strong>placeholder</strong> - Placeholder text</li>
            <li><strong>rows</strong> - Number of rows for textarea</li>
            <li><strong>hx_*</strong> - All HTMX attributes (use underscores)</li>
            <li><strong>on*</strong> - JavaScript event handlers (onkeyup, onchange, etc.)</li>
            <li><strong>required</strong> - Make field required</li>
            <li><strong>readonly</strong> - Make field read-only</li>
            <li><strong>disabled</strong> - Disable field</li>
        </ul>
    </div>
</div>

<style>
.demo-form {
    max-width: 1200px;
    margin: 0 auto;
}

.form-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

.form-section h3 {
    margin-top: 0;
    color: #1976d2;
    border-bottom: 2px solid #1976d2;
    padding-bottom: 8px;
}

.form-actions {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
}

.form-actions .mdc-button {
    margin: 0 10px;
}

.usage-examples {
    margin-top: 60px;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 8px;
}

.usage-examples pre {
    background: #fff;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    border-left: 4px solid #1976d2;
}

.usage-examples code {
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.usage-examples ul {
    padding-left: 20px;
}

.usage-examples li {
    margin-bottom: 8px;
}
</style>
{% endblock %}
