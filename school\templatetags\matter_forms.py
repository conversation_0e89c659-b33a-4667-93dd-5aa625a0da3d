from django import template
from django.forms import <PERSON><PERSON><PERSON><PERSON>
from django.utils.html import format_html, escape
from django.utils.safestring import mark_safe
import json

register = template.Library()


@register.simple_tag
def matter_field(field, **kwargs):
    """
    Render a Django form field using Matter CSS styling.
    Supports text inputs, textareas, and select fields.

    Usage:
    {% matter_field form.field_name class="small" type="email" %}
    {% matter_field form.field_name attrs='{"hx-get": "/api/endpoint/", "hx-target": "#result"}' %}
    {% matter_field form.field_name class="small" attrs='{"hx-get": "/api/", "data-value": "test"}' %}
    """
    if not isinstance(field, BoundField):
        return ""

    # Extract special parameters
    css_class = kwargs.get('class', '')
    placeholder = kwargs.get('placeholder', field.label or '')
    attrs_param = kwargs.get('attrs', {})

    # Parse attrs if it's a string (JSON)
    if isinstance(attrs_param, str):
        try:
            attrs_param = json.loads(attrs_param)
        except (json.JSONDecodeError, ValueError):
            attrs_param = {}

    # Start with the field's widget attributes
    attrs = {}
    if hasattr(field.field.widget, 'attrs') and field.field.widget.attrs:
        attrs.update(field.field.widget.attrs)

    # Build additional attributes from kwargs
    for key, value in kwargs.items():
        if key not in ['class', 'placeholder', 'attrs']:
            # Convert underscores to hyphens for HTML attributes
            html_key = key.replace('_', '-')
            attrs[html_key] = value

    # Merge attrs parameter (this will override widget attrs if there are conflicts)
    attrs.update(attrs_param)

    # Determine field type and render accordingly
    widget_type = field.field.widget.__class__.__name__.lower()

    if 'select' in widget_type:
        return render_matter_select(field, css_class, attrs)
    elif 'textarea' in widget_type:
        return render_matter_textarea(field, css_class, placeholder, attrs)
    else:
        return render_matter_input(field, css_class, placeholder, attrs)


def build_attrs_string(attrs):
    """Build HTML attributes string from dictionary, escaping values."""
    if not attrs:
        return ''

    attr_parts = []
    for key, value in attrs.items():
        if value is not None and value != '':
            # Escape the value for HTML safety
            escaped_value = str(value).replace('"', '&quot;')
            attr_parts.append(f'{key}="{escaped_value}"')

    return ' '.join(attr_parts)


def render_matter_input(field, css_class, placeholder, attrs):
    """Render a Matter CSS text input field."""

    # Separate container attributes from field attributes
    container_attrs = {}
    field_attrs = {}

    for key, value in attrs.items():
        if key in ['hx-swap-oob', 'hx-swap']:
            container_attrs[key] = value
        else:
            field_attrs[key] = value

    # Build attributes strings
    container_attrs_str = build_attrs_string(container_attrs)
    field_attrs_str = build_attrs_string(field_attrs)

    # Get field value
    value = field.value() if field.value() is not None else ''

    # Build required attribute
    required = 'required' if field.field.required else ''

    # Check for small variant
    small_class = 'small' if 'small' in css_class else ''

    # Determine input type and check for date fields
    input_type = field_attrs.get('type', 'text')
    is_date_field = False

    if input_type == 'text':  # Only auto-detect if type not explicitly set
        if 'email' in field.name.lower():
            input_type = 'email'
        elif 'password' in field.name.lower():
            input_type = 'password'
        elif 'number' in field.name.lower() or 'age' in field.name.lower():
            input_type = 'number'
        elif 'date' in field.name.lower():
            input_type = 'text'  # Use text input for Flatpickr
            is_date_field = True
    elif input_type == 'date':
        # If explicitly set as date, also use Flatpickr
        input_type = 'text'
        is_date_field = True

    # For date fields, make input readonly to force Flatpickr usage
    readonly_attr = 'readonly="readonly"' if is_date_field else ''

    html = f'''
    <div class="matter-textfield-outlined {small_class} {css_class}" id="{field.id_for_label}_container" {container_attrs_str}>
        <input
            type="{input_type}"
            name="{field.html_name}"
            id="{field.id_for_label}"
            value="{escape(value)}"
            placeholder=" "
            {required}
            {readonly_attr}
            {field_attrs_str}
        >
        <span>{field.label}<span class='matter-required-indicator'>{"*" if field.field.required else ""}</span></span>
    </div>
    '''

    # Add autofill detection script for all fields
    html += f'''
    <script>
        (function() {{
            const input = document.getElementById('{field.id_for_label}');
            const container = document.getElementById('{field.id_for_label}_container');

            if (input && container) {{
                // Function to check if field has value and update label
                function updateLabelState() {{
                    if (input.value && input.value.trim() !== '') {{
                        container.classList.add('has-value');
                    }} else {{
                        container.classList.remove('has-value');
                    }}
                }}

                // Check initial state
                updateLabelState();

                // Listen for input changes
                input.addEventListener('input', updateLabelState);
                input.addEventListener('change', updateLabelState);

                // Handle autofill detection
                // Method 1: Check for autofill pseudo-class
                function checkAutofill() {{
                    if (input.matches(':-webkit-autofill') || input.matches(':autofill')) {{
                        container.classList.add('has-value');
                    }}
                }}

                // Method 2: Monitor for value changes without user input
                let lastValue = input.value;
                function detectAutofill() {{
                    if (input.value !== lastValue && input.value !== '') {{
                        container.classList.add('has-value');
                    }}
                    lastValue = input.value;
                }}

                // Run autofill checks
                setTimeout(checkAutofill, 100);
                setTimeout(detectAutofill, 100);
                setTimeout(updateLabelState, 200);

                // Also check when the page becomes visible (handles delayed autofill)
                document.addEventListener('visibilitychange', function() {{
                    if (!document.hidden) {{
                        setTimeout(updateLabelState, 100);
                        setTimeout(checkAutofill, 100);
                    }}
                }});

                // Check on window load (for slower autofill)
                window.addEventListener('load', function() {{
                    setTimeout(updateLabelState, 100);
                    setTimeout(checkAutofill, 100);
                }});
            }}
        }})();
    </script>
    '''

    # Add Flatpickr initialization for date fields
    if is_date_field:
        html += f'''
    <script>
        (function() {{
            // Wait for Flatpickr to be available
            function initFlatpickr() {{
                if (typeof flatpickr !== 'undefined') {{
                    const input = document.getElementById('{field.id_for_label}');
                    if (input && !input._flatpickr) {{
                        flatpickr("#{field.id_for_label}", {{
                            dateFormat: "d/m/Y",
                            enableTime: false,
                            timeZone: "Africa/Abidjan",
                            allowInput: true,
                            timeZone: "Africa/Abidjan",
                            disableMobile: "true",
                            locale: "fr",
                            maxDate: 'today'
                        }});
                    }}
                }} else {{
                    // Retry after a short delay if Flatpickr is not yet loaded
                    setTimeout(initFlatpickr, 100);
                }}
            }}

            // Initialize when DOM is ready
            if (document.readyState === 'loading') {{
                document.addEventListener('DOMContentLoaded', initFlatpickr);
            }} else {{
                initFlatpickr();
            }}
        }})();
    </script>
        '''

    # Add error messages if any
    if field.errors:
        html += '<div class="matter-field-errors">'
        for error in field.errors:
            html += f'<div class="matter-error-text">{error}</div>'
        html += '</div>'

    return mark_safe(html)


def render_matter_textarea(field, css_class, placeholder, attrs):
    """Render a Matter CSS textarea field."""

    # Separate container attributes from field attributes
    container_attrs = {}
    field_attrs = {}

    for key, value in attrs.items():
        if key in ['hx-swap-oob', 'hx-swap']:
            container_attrs[key] = value
        else:
            field_attrs[key] = value

    # Build attributes strings
    container_attrs_str = build_attrs_string(container_attrs)
    field_attrs_str = build_attrs_string(field_attrs)

    # Get field value
    value = field.value() if field.value() is not None else ''

    # Build required attribute
    required = 'required' if field.field.required else ''

    # Check for small variant
    small_class = 'small' if 'small' in css_class else ''

    html = f'''
    <div class="matter-textfield-outlined {small_class} {css_class}" id="{field.id_for_label}_container" {container_attrs_str}>
        <textarea
            name="{field.html_name}"
            id="{field.id_for_label}"
            placeholder=" "
            {required}
            {field_attrs_str}
        >{value}</textarea>
        <span>{field.label}<span class='matter-required-indicator'>{"*" if field.field.required else ""}</span></span>
    </div>
    '''

    # Add autofill detection script for textarea
    html += f'''
    <script>
        (function() {{
            const textarea = document.getElementById('{field.id_for_label}');
            const container = document.getElementById('{field.id_for_label}_container');

            if (textarea && container) {{
                // Function to check if field has value and update label
                function updateLabelState() {{
                    if (textarea.value && textarea.value.trim() !== '') {{
                        container.classList.add('has-value');
                    }} else {{
                        container.classList.remove('has-value');
                    }}
                }}

                // Check initial state
                updateLabelState();

                // Listen for input changes
                textarea.addEventListener('input', updateLabelState);
                textarea.addEventListener('change', updateLabelState);

                // Handle autofill detection
                function checkAutofill() {{
                    if (textarea.matches(':-webkit-autofill') || textarea.matches(':autofill')) {{
                        container.classList.add('has-value');
                    }}
                }}

                // Monitor for value changes without user input
                let lastValue = textarea.value;
                function detectAutofill() {{
                    if (textarea.value !== lastValue && textarea.value !== '') {{
                        container.classList.add('has-value');
                    }}
                    lastValue = textarea.value;
                }}

                // Run autofill checks
                setTimeout(checkAutofill, 100);
                setTimeout(detectAutofill, 100);
                setTimeout(updateLabelState, 200);

                // Also check when the page becomes visible
                document.addEventListener('visibilitychange', function() {{
                    if (!document.hidden) {{
                        setTimeout(updateLabelState, 100);
                        setTimeout(checkAutofill, 100);
                    }}
                }});

                // Check on window load
                window.addEventListener('load', function() {{
                    setTimeout(updateLabelState, 100);
                    setTimeout(checkAutofill, 100);
                }});
            }}
        }})();
    </script>
    '''

    # Add error messages if any
    if field.errors:
        html += '<div class="matter-field-errors">'
        for error in field.errors:
            html += f'<div class="matter-error-text">{error}</div>'
        html += '</div>'

    return mark_safe(html)


def render_matter_select(field, css_class, attrs):
    """Render a Matter CSS select field."""

    # Separate container attributes from field attributes
    container_attrs = {}
    field_attrs = {}

    for key, value in attrs.items():
        if key in ['hx-swap-oob', 'hx-swap']:
            container_attrs[key] = value
        else:
            field_attrs[key] = value

    # Build attributes strings
    container_attrs_str = build_attrs_string(container_attrs)
    field_attrs_str = build_attrs_string(field_attrs)

    # Get field value
    selected_value = field.value() if field.value() is not None else ''

    # Build required attribute
    required = 'required' if field.field.required else ''

    # Check for small variant
    small_class = 'small' if 'small' in css_class else ''

    # Get choices
    choices = field.field.choices

    html = f'''
    <div class="matter-select-wrapper {small_class} {css_class}" id="{field.id_for_label}_container" {container_attrs_str}>
        <select
            name="{field.html_name}"
            id="{field.id_for_label}"
            class="matter-select"
            {required}
            {field_attrs_str}
        >
    '''
    
    # Add empty option if not required or no value selected
    if not field.field.required or not selected_value:
        html += '<option value="">------</option>'
    
    # Add options
    for value, label in choices:
        if value == '':  # Skip empty choice if already added
            continue
        selected_attr = 'selected' if str(value) == str(selected_value) else ''
        html += f'<option value="{value}" {selected_attr}>{label}</option>'
    
    html += f'''
        </select>
        <label class="matter-select-label">{field.label}<span class='matter-required-indicator'>{"*" if field.field.required else ""}</span></label>
    </div>
    '''
    
    # Add JavaScript for dropdown arrow animation
    html += f'''
    <script>
        (function() {{
            const wrapper = document.getElementById('{field.id_for_label}_container');
            const select = document.getElementById('{field.id_for_label}');

            if (wrapper && select) {{
                let isOpen = false;

                // Handle select opening
                select.addEventListener('mousedown', function() {{
                    isOpen = !isOpen;
                    wrapper.setAttribute('data-open', isOpen);
                }});

                // Handle select closing
                select.addEventListener('blur', function() {{
                    isOpen = false;
                    wrapper.setAttribute('data-open', 'false');
                }});

                // Function to check if field has value and update label
                function updateLabelState() {{
                    if (select.value && select.value.trim() !== '') {{
                        select.classList.add('has-value');
                    }} else {{
                        select.classList.remove('has-value');
                    }}
                }}

                // Handle value changes to manage label floating
                select.addEventListener('change', updateLabelState);

                // Handle autofill detection for select fields
                function checkAutofill() {{
                    if (select.matches(':-webkit-autofill') || select.matches(':autofill')) {{
                        select.classList.add('has-value');
                    }}
                }}

                // Monitor for value changes without user input
                let lastValue = select.value;
                function detectAutofill() {{
                    if (select.value !== lastValue && select.value !== '') {{
                        select.classList.add('has-value');
                    }}
                    lastValue = select.value;
                }}

                // Set initial state and run autofill checks
                updateLabelState();
                setTimeout(checkAutofill, 100);
                setTimeout(detectAutofill, 100);
                setTimeout(updateLabelState, 200);

                // Also check when the page becomes visible
                document.addEventListener('visibilitychange', function() {{
                    if (!document.hidden) {{
                        setTimeout(updateLabelState, 100);
                        setTimeout(checkAutofill, 100);
                    }}
                }});

                // Check on window load
                window.addEventListener('load', function() {{
                    setTimeout(updateLabelState, 100);
                    setTimeout(checkAutofill, 100);
                }});
            }}
        }})();
    </script>
    '''
    
    # Add error messages if any
    if field.errors:
        html += '<div class="matter-field-errors">'
        for error in field.errors:
            html += f'<div class="matter-error-text">{error}</div>'
        html += '</div>'
    
    return mark_safe(html)


@register.simple_tag
def matter_field_htmx(field, **kwargs):
    """
    Convenience template tag for Matter fields with HTMX attributes.
    Automatically converts underscore-separated HTMX attributes to hyphenated ones.

    Usage:
    {% matter_field_htmx form.field_name class="small" hx_get="/api/endpoint/" hx_target="#result" hx_trigger="keyup delay:500ms" %}
    """
    # Convert HTMX underscore attributes to hyphens
    htmx_attrs = {}
    other_kwargs = {}

    for key, value in kwargs.items():
        if key.startswith('hx_') or key.startswith('data_'):
            # Convert hx_get to hx-get, data_value to data-value, etc.
            html_key = key.replace('_', '-')
            htmx_attrs[html_key] = value
        else:
            other_kwargs[key] = value

    # Add HTMX attributes to the attrs parameter
    if htmx_attrs:
        existing_attrs = other_kwargs.get('attrs', {})
        if isinstance(existing_attrs, str):
            try:
                existing_attrs = json.loads(existing_attrs)
            except (json.JSONDecodeError, ValueError):
                existing_attrs = {}

        existing_attrs.update(htmx_attrs)
        other_kwargs['attrs'] = existing_attrs

    return matter_field(field, **other_kwargs)


def render_matter_checkbox(field, css_class, label_text, attrs):
    """
    Render a checkbox field using Matter CSS styling.
    """
    # Get field value
    value = field.value() if field.value() is not None else False
    checked = 'checked' if value else ''

    # Handle required attribute
    required = 'required' if field.field.required else ''

    # Handle container attributes for hx-swap-oob
    container_attrs = {}
    field_attrs = {}

    for key, value in attrs.items():
        if key == 'hx-swap-oob':
            container_attrs[key] = value
        else:
            field_attrs[key] = value

    # Convert attributes to strings
    container_attrs_str = ' '.join([f'{k}="{v}"' for k, v in container_attrs.items()])
    field_attrs_str = ' '.join([f'{k}="{v}"' for k, v in field_attrs.items()])

    # Add space before attributes if they exist
    if container_attrs_str:
        container_attrs_str = ' ' + container_attrs_str
    if field_attrs_str:
        field_attrs_str = ' ' + field_attrs_str

    # Determine size class
    small_class = 'small' if 'small' in css_class else ''
    extra_small_class = 'extra-small' if 'extra-small' in css_class else ''

    html = f'''
    <div class="matter-checkbox {small_class} {extra_small_class} {css_class}" id="{field.id_for_label}_container" {container_attrs_str}>
        <input type="checkbox"
            name="{field.html_name}"
            id="{field.id_for_label}"
            {checked}
            {required}
            {field_attrs_str}
        >
        <span>{label_text or field.label}<span class='matter-required-indicator'>{"*" if field.field.required else ""}</span></span>
    </div>
    '''

    return mark_safe(html)


@register.simple_tag
def matter_checkbox(field, **kwargs):
    """
    Render a Django form checkbox field using Matter CSS styling.

    Usage:
    {% matter_checkbox form.field_name class="small" label="Custom Label" %}
    {% matter_checkbox form.field_name class="extra-small" hx_trigger="change" %}
    """
    if not isinstance(field, BoundField):
        return ""

    # Extract special parameters
    css_class = kwargs.get('class', '')
    label_text = kwargs.get('label', field.label or '')
    attrs_param = kwargs.get('attrs', {})

    # Parse attrs if it's a string (JSON)
    if isinstance(attrs_param, str):
        try:
            attrs_param = json.loads(attrs_param)
        except (json.JSONDecodeError, ValueError):
            attrs_param = {}

    # Start with the field's widget attributes
    attrs = {}
    if hasattr(field.field.widget, 'attrs') and field.field.widget.attrs:
        attrs.update(field.field.widget.attrs)

    # Build additional attributes from kwargs
    for key, value in kwargs.items():
        if key not in ['class', 'label', 'attrs']:
            # Convert underscores to hyphens for HTML attributes
            html_key = key.replace('_', '-')
            attrs[html_key] = value

    # Merge attrs parameter (this will override widget attrs if there are conflicts)
    attrs.update(attrs_param)

    return render_matter_checkbox(field, css_class, label_text, attrs)
