{% load mdc_forms %}

<div id="student-form-content">
    <div class="form-section">
        <h3>Student Information</h3>
        
        <!-- Student ID -->
        <div class="row">
            <div class="form-field col-md-6">
                {% mdc_field form.student_id class="mdc-small-input" onkeyup="this.value = this.value.toUpperCase();" %}
            </div>
        </div>
        
        <!-- Name Fields -->
        <div class="row">
            <div class="form-field col-md-6">
                {% mdc_field form.first_name class="mdc-small-input" onkeyup="this.value = this.value.toUpperCase();" %}
            </div>
            <div class="form-field col-md-6">
                {% mdc_field form.last_name class="mdc-small-input" onkeyup="this.value = this.value.toUpperCase();" %}
            </div>
        </div>
        
        <!-- Email and Gender -->
        <div class="row">
            <div class="form-field col-md-6">
                {% mdc_field form.email type="email" %}
            </div>
            <div class="form-field col-md-6">
                {% mdc_select_field form.gender %}
            </div>
        </div>
        
        <!-- Birth Date -->
        <div class="row">
            <div class="form-field col-md-6">
                {% mdc_field form.birth_date type="date" %}
            </div>
            <div class="form-field col-md-6">
                {% mdc_checkbox_field form.active %}
            </div>
        </div>
        
        <!-- Notes -->
        <div class="row">
            <div class="form-field col-md-12">
                {% mdc_textarea_field form.notes rows="3" %}
            </div>
        </div>
    </div>
</div>

<style>
.form-section {
    padding: 20px;
}

.form-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #1976d2;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 8px;
}

.form-field {
    margin-bottom: 16px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 8px;
}

.col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0 8px;
}

@media (max-width: 768px) {
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Custom styling for demo */
.mdc-small-input {
    font-size: 14px;
}

.mdc-text-field--outlined .mdc-text-field__input {
    padding: 12px 16px 14px;
}

.mdc-text-field--outlined .mdc-floating-label {
    font-size: 14px;
}

.mdc-text-field--outlined .mdc-floating-label--float-above {
    font-size: 12px;
}
</style>
