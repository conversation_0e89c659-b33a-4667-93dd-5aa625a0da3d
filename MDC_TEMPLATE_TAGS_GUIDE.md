# MDC Form Template Tags Guide

This guide explains how to use the new Material Design Components (MDC) template tags for rendering Django form fields with Material Design styling.

## Overview

The MDC template tags provide a simple, widget_tweaks-like interface for rendering Django form fields as Material Design components. They automatically handle:

- ✅ **Material Design styling** - Proper MDC structure and classes
- ✅ **Field type detection** - Automatically chooses the right component
- ✅ **Error handling** - Shows validation errors with proper styling
- ✅ **HTMX integration** - Supports all HTMX attributes
- ✅ **JavaScript events** - Supports onclick, onkeyup, etc.
- ✅ **Custom attributes** - Add any HTML attributes

## Installation

1. **Load the template tags** in your template:
```html
{% load mdc_forms %}
```

2. **Use the tags** to render form fields:
```html
{% mdc_field form.field_name %}
```

## Available Template Tags

### `{% mdc_field %}`
Auto-detects field type and renders the appropriate Material Design component.

```html
<!-- Basic usage -->
{% mdc_field form.first_name %}

<!-- With custom attributes -->
{% mdc_field form.email type="email" class="mdc-small-input" %}
```

### `{% mdc_text_field %}`
Specifically renders as a Material Design text field.

```html
{% mdc_text_field form.name class="mdc-small-input" %}
```

### `{% mdc_select_field %}`
Specifically renders as a Material Design select field.

```html
{% mdc_select_field form.country %}
```

### `{% mdc_textarea_field %}`
Specifically renders as a Material Design textarea.

```html
{% mdc_textarea_field form.bio rows="4" %}
```

### `{% mdc_checkbox_field %}`
Specifically renders as a Material Design checkbox.

```html
{% mdc_checkbox_field form.newsletter %}
```

## Usage Examples

### Basic Form Fields

```html
{% load mdc_forms %}

<form method="post">
    {% csrf_token %}
    
    <!-- Text fields -->
    <div class="form-field">
        {% mdc_field form.first_name class="mdc-small-input" %}
    </div>
    
    <div class="form-field">
        {% mdc_field form.email type="email" %}
    </div>
    
    <!-- Select field -->
    <div class="form-field">
        {% mdc_select_field form.country %}
    </div>
    
    <!-- Textarea -->
    <div class="form-field">
        {% mdc_textarea_field form.bio rows="4" %}
    </div>
    
    <!-- Checkbox -->
    <div class="form-field">
        {% mdc_checkbox_field form.newsletter %}
    </div>
</form>
```

### Advanced Usage with HTMX

```html
<!-- Field with HTMX integration -->
{% mdc_field form.student_id
    class="mdc-small-input"
    hx_get="/check-student/"
    hx_target="#student-info"
    hx_trigger="keyup delay:1s" %}

<!-- Select with dynamic loading -->
{% mdc_select_field form.level
    hx_get="/get-fees/"
    hx_target="#fees-section"
    hx_trigger="change" %}

<!-- Field with JavaScript events -->
{% mdc_field form.name
    onkeyup="this.value = this.value.toUpperCase();"
    onfocus="console.log('Field focused')" %}
```

### Grid Layout Example

```html
<div class="row">
    <div class="form-field col-md-6">
        {% mdc_field form.first_name class="mdc-small-input" %}
    </div>
    <div class="form-field col-md-6">
        {% mdc_field form.last_name class="mdc-small-input" %}
    </div>
</div>

<div class="row">
    <div class="form-field col-md-4">
        {% mdc_field form.age type="number" %}
    </div>
    <div class="form-field col-md-4">
        {% mdc_field form.birth_date type="date" %}
    </div>
    <div class="form-field col-md-4">
        {% mdc_select_field form.gender %}
    </div>
</div>
```

## Supported Attributes

### Common Attributes
- **`class`** - Additional CSS classes
- **`type`** - Input type (text, email, number, date, etc.)
- **`placeholder`** - Placeholder text (though MDC uses floating labels)
- **`required`** - Make field required
- **`readonly`** - Make field read-only
- **`disabled`** - Disable field

### HTMX Attributes
All HTMX attributes are supported (use underscores instead of hyphens):
- **`hx_get`**, **`hx_post`**, **`hx_put`**, **`hx_delete`**
- **`hx_target`**, **`hx_swap`**, **`hx_trigger`**
- **`hx_include`**, **`hx_vals`**, **`hx_headers`**
- And all other HTMX attributes (replace hyphens with underscores)

### JavaScript Event Handlers
- **`onkeyup`**, **`onkeydown`**, **`onchange`**
- **`onclick`**, **`onfocus`**, **`onblur`**
- **`onsubmit`**, **`oninput`**

### Field-Specific Attributes
- **`rows`** - Number of rows for textarea fields
- **`maxlength`**, **`minlength`** - Text length constraints
- **`min`**, **`max`** - Number field constraints
- **`step`** - Number field step value

## Field Type Detection

The template tags automatically detect the appropriate Material Design component based on the Django field type:

| Django Field Type | MDC Component | Notes |
|------------------|---------------|-------|
| `CharField` | Text Field | Basic text input |
| `EmailField` | Text Field | With type="email" |
| `URLField` | Text Field | With type="url" |
| `IntegerField` | Text Field | With type="number" |
| `DateField` | Text Field | With type="date" |
| `TimeField` | Text Field | With type="time" |
| `DateTimeField` | Text Field | With type="datetime-local" |
| `TextField` | Textarea | Multi-line text |
| `ChoiceField` | Select | Dropdown selection |
| `BooleanField` | Checkbox | Checkbox input |

## Error Handling

Fields with validation errors automatically display error messages and styling:

```html
<!-- Field with error will render like this: -->
<div class="mdc-text-field mdc-text-field--outlined mdc-text-field--invalid">
    <input class="mdc-text-field__input" type="text" id="id_email" name="email">
    <div class="mdc-notched-outline">
        <div class="mdc-notched-outline__leading"></div>
        <div class="mdc-notched-outline__notch">
            <label class="mdc-floating-label" for="id_email">Email</label>
        </div>
        <div class="mdc-notched-outline__trailing"></div>
    </div>
    <div class="mdc-text-field-helper-line">
        <div class="mdc-text-field-helper-text mdc-text-field-helper-text--validation-msg">
            Enter a valid email address.
        </div>
    </div>
</div>
```

## Migration from widget_tweaks

### Before (widget_tweaks)
```html
{% load widget_tweaks %}

{% render_field form.name class='form-control' %}
{% render_field form.email class='form-control' type='email' %}
{% render_field form.country class='form-control' %}
```

### After (MDC template tags)
```html
{% load mdc_forms %}

{% mdc_field form.name class="mdc-small-input" %}
{% mdc_field form.email type="email" %}
{% mdc_select_field form.country %}
```

## Best Practices

1. **Use consistent classes**: Apply `mdc-small-input` for compact forms
2. **Wrap in form-field divs**: Use `.form-field` containers for spacing
3. **Use grid system**: Leverage the 12-column grid for layouts
4. **Handle errors gracefully**: Let the template tags handle error display
5. **Test HTMX integration**: Ensure HTMX attributes work as expected

## Example: Complete Student Form

```html
{% load mdc_forms %}

<form method="post" hx-post="{% url 'student_create' %}" hx-target="#student-modal-content">
    {% csrf_token %}
    
    <div class="row">
        <div class="form-field col-md-6">
            {% mdc_field form.student_id class="mdc-small-input" onkeyup="this.value = this.value.toUpperCase();" %}
        </div>
        <div class="form-field col-md-6">
            {% mdc_select_field form.gender %}
        </div>
    </div>
    
    <div class="row">
        <div class="form-field col-md-6">
            {% mdc_field form.first_name class="mdc-small-input" onkeyup="this.value = this.value.toUpperCase();" %}
        </div>
        <div class="form-field col-md-6">
            {% mdc_field form.last_name class="mdc-small-input" onkeyup="this.value = this.value.toUpperCase();" %}
        </div>
    </div>
    
    <div class="row">
        <div class="form-field col-md-6">
            {% mdc_field form.email type="email" %}
        </div>
        <div class="form-field col-md-6">
            {% mdc_field form.birth_date type="date" %}
        </div>
    </div>
    
    <div class="row">
        <div class="form-field col-md-12">
            {% mdc_textarea_field form.notes rows="3" %}
        </div>
    </div>
    
    <div class="row">
        <div class="form-field col-md-6">
            {% mdc_checkbox_field form.active %}
        </div>
    </div>
</form>
```

This new system provides the same ease of use as widget_tweaks but with proper Material Design styling and automatic component initialization!
