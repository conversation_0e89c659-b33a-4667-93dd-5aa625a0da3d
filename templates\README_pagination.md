# Material Design M2 Data Table Pagination Component

This is a reusable Material Design M2 pagination component that's fully compatible with Django's pagination system and HTMX for dynamic loading.

## Features

- **Material Design M2 Compliant**: Follows Google's Material Design M2 specifications for data table pagination
- **Django Integration**: Works seamlessly with Django's `Paginator` and `Page` objects
- **HTMX Compatible**: Supports dynamic content loading without page refreshes
- **Responsive Design**: Adapts to mobile and desktop screens
- **Customizable**: Supports custom per-page options and styling
- **Accessible**: Includes proper ARIA labels and keyboard navigation
- **Dark Theme Support**: Includes dark theme styles

## Usage

### Basic Usage

```django
{% include 'material/pagination.html' with page_obj=page_obj per_page=per_page %}
```

### Advanced Usage with HTMX

```django
{% include 'material/pagination.html' with page_obj=page_obj per_page=per_page include_items='.mdc-text-field__input,.mdc-select' target='#content-area' %}
```

## Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `page_obj` | Django Page object | Yes | - | The pagination object from Django's ListView |
| `per_page` | String/Integer | No | "10" | Current items per page value |
| `include_items` | String | No | - | HTMX include selector for form fields |
| `hx_vals` | String | No | - | Additional HTMX values as JSON string |
| `target` | String | No | "#content-area" | HTMX target selector |
| `per_page_options` | List | No | [10, 25, 50, 100] | Available per-page options |

## Django View Setup

Your Django view should inherit from `ListView` and include pagination:

```python
class StudentsListView(ListView):
    model = MyModel
    template_name = 'my_template.html'
    paginate_by = 10
    
    def get_paginate_by(self, queryset):
        return self.request.GET.get('per_page', 10)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['per_page'] = self.request.GET.get('per_page', '10')
        return context
```

## Template Integration

### In your data table template:

```django
<div class="mdc-data-table">
    <div class="mdc-data-table__table-container">
        <table class="mdc-data-table__table">
            <!-- Your table content -->
        </table>
    </div>
    
    <!-- Include pagination -->
    {% include 'material/pagination.html' with page_obj=page_obj per_page=per_page %}
</div>
```

### With search and filters:

```django
{% include 'material/pagination.html' with page_obj=page_obj per_page=per_page include_items='.search-input,.filter-select' %}
```

## CSS Classes

The component uses these main CSS classes:

- `.mdc-data-table__pagination` - Main pagination container
- `.mdc-data-table__pagination-trailing` - Content wrapper
- `.mdc-data-table__pagination-rows-per-page` - Per-page selector container
- `.mdc-data-table__pagination-navigation` - Navigation controls container
- `.mdc-data-table__pagination-buttons` - Button container
- `.mdc-data-table__pagination-page-numbers` - Page numbers container

## Customization

### Custom Per-Page Options

Pass a custom list of options:

```django
{% include 'material/pagination.html' with page_obj=page_obj per_page=per_page per_page_options="5,15,30,60" %}
```

### Custom Colors

The component respects CSS custom properties:

```css
:root {
    --pagination-primary-color: #4caf50;
    --pagination-primary-rgb: 76, 175, 80;
}
```

### Dark Theme

Add `data-theme="dark"` to your body or container element:

```html
<body data-theme="dark">
```

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Dependencies

- Material Design Components Web (MDC-Web)
- HTMX (for dynamic loading)
- Material Icons font

## Examples

### Basic Students List

```django
<!-- students_list.html -->
<div class="mdc-data-table">
    <div class="mdc-data-table__table-container">
        <table class="mdc-data-table__table">
            <thead>
                <tr class="mdc-data-table__header-row">
                    <th class="mdc-data-table__header-cell">Name</th>
                    <th class="mdc-data-table__header-cell">Email</th>
                </tr>
            </thead>
            <tbody class="mdc-data-table__content">
                {% for student in students %}
                <tr class="mdc-data-table__row">
                    <td class="mdc-data-table__cell">{{ student.name }}</td>
                    <td class="mdc-data-table__cell">{{ student.email }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    {% include 'material/pagination.html' with page_obj=page_obj per_page=per_page %}
</div>
```

### With Search and Filters

```django
<div class="table-controls">
    <input type="text" class="mdc-text-field__input search-input" name="search" value="{{ search }}">
    <select class="mdc-select filter-select" name="status">
        <option value="">All</option>
        <option value="active">Active</option>
        <option value="inactive">Inactive</option>
    </select>
</div>

<div class="mdc-data-table">
    <!-- Table content -->
    {% include 'material_pagination.html' with page_obj=page_obj per_page=per_page include_items='.search-input,.filter-select' %}
</div>
```

## Troubleshooting

### Pagination not working
- Ensure `page_obj` is passed to the template
- Check that your view inherits from `ListView` or manually creates pagination
- Verify HTMX is loaded and configured correctly

### Styling issues
- Make sure `static/material/css/pagination.css` is loaded
- Check that Material Design Components CSS is included
- Verify CSS custom properties are defined

### JavaScript errors
- Ensure MDC-Web JavaScript is loaded
- Check browser console for specific errors
- Verify Material Icons font is loaded
